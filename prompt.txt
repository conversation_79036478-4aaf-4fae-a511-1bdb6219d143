# **CS2 Betting Analysis: Enhanced Prediction Framework with GRID API Integration**

**Objective**  
Generate the most accurate CS2 match predictions using real-time GRID API data combined with HLTV analysis, featuring systematic methodology, quantifiable metrics, and maximum value extraction for optimal betting decisions.

**⚠️ CRITICAL: NO MOCK OR SIMULATED DATA** - Only use actual, live data from GRID API and verified sources. Any analysis must be based on real statistics and current match data.

---

## **DATE SELECTION OPTIONS**

**Choose Analysis Period:**
- **TODAY** - Analyze matches scheduled for current date (UTC)
- **TOMORROW** - Analyze matches scheduled for next day (UTC)  
- **CUSTOM** - Specify exact date range (Format: YYYY-MM-DD to YYYY-MM-DD)

**Implementation:**
```javascript
const dateOptions = {
  TODAY: {
    start: moment().utc().startOf('day').toISOString(),
    end: moment().utc().endOf('day').toISOString()
  },
  TOMORROW: {
    start: moment().utc().add(1, 'day').startOf('day').toISOString(),
    end: moment().utc().add(1, 'day').endOf('day').toISOString()
  },
  CUSTOM: {
    start: '[USER_INPUT_START_DATE]T00:00:00.000Z',
    end: '[USER_INPUT_END_DATE]T23:59:59.999Z'
  }
};
```

---

## 1. **GRID API Data Extraction Protocol (PRIMARY)**

My GRID API KEY IS cDXLTwMg735stCkxIaaAEdlkYlkKcu88dwjshjYn

### GRID API Configuration:
```
API Endpoint: https://api-op.grid.gg/central-data/graphql
Authentication: x-api-key: cDXLTwMg735stCkxIaaAEdlkYlkKcu88dwjshjYn
Rate Limits: 40 req/min (Closed Platform) | 20 req/min (Open Platform)
Max Results: 50 per query (use pagination for larger datasets)
Game Title: "counter-strike-2" (CS2 specific queries)
```

### **VALIDATED GRID API SCHEMAS FOR CS2:**

#### 1. **Match/Series Query** (VERIFIED SCHEMA)
```graphql
query GetCS2Matches($dateFilter: DateTimeFilter!) {
  series(
    filter: {
      videogame: { id: "counter-strike-2" }
      scheduledAt: $dateFilter
      state: SCHEDULED
    }
    first: 50
  ) {
    edges {
      node {
        id
        name
        bestOf
        scheduledAt
        state
        tournament {
          id
          name
          tier
        }
        opponents {
          id
          name
          image
          team {
            id
            name
            image
            players {
              id
              name
              image
            }
          }
        }
        games {
          id
          position
          state
          map {
            id
            name
            image
          }
          opponents {
            score
            team {
              id
              name
            }
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
```

#### 2. **Team Statistics** (VERIFIED SCHEMA)
```graphql
query GetTeamStats($teamId: ID!, $dateRange: DateTimeFilter!) {
  team(id: $teamId) {
    id
    name
    image
    players {
      id
      name
      image
      nationality
    }
    stats(
      filter: {
        videogame: { id: "counter-strike-2" }
        period: $dateRange
      }
    ) {
      ... on CounterStrike2TeamStats {
        gamesCount
        gamesWonCount
        gamesLostCount
        seriesCount
        seriesWonCount
        seriesLostCount
        roundsCount
        roundsWonCount
        roundsLostCount
        kills
        deaths
        assists
        headshots
        headshotPercentage
        kdr
        adr
        rating
        firstKills
        firstDeaths
        multiKills {
          k2
          k3
          k4
          k5
        }
        clutches {
          won1v1
          won1v2
          won1v3
          won1v4
          won1v5
          total1v1
          total1v2
          total1v3
          total1v4
          total1v5
        }
      }
    }
  }
}
```

#### 3. **Player Statistics** (VERIFIED SCHEMA)
```graphql
query GetPlayerStats($playerId: ID!, $dateRange: DateTimeFilter!) {
  player(id: $playerId) {
    id
    name
    image
    nationality
    team {
      id
      name
    }
    stats(
      filter: {
        videogame: { id: "counter-strike-2" }
        period: $dateRange
      }
    ) {
      ... on CounterStrike2PlayerStats {
        gamesCount
        gamesWonCount
        gamesLostCount
        roundsCount
        roundsWonCount
        roundsLostCount
        kills
        deaths
        assists
        headshots
        headshotPercentage
        kdr
        adr
        rating
        firstKills
        firstDeaths
        clutchesWon
        clutchesTotal
        multiKills {
          k2
          k3
          k4
          k5
        }
        maps {
          mapId
          mapName
          gamesCount
          rating
          kdr
          adr
        }
      }
    }
  }
}
```

#### 4. **Head-to-Head Analysis** (VERIFIED SCHEMA)
```graphql
query GetHeadToHead($team1Id: ID!, $team2Id: ID!, $dateRange: DateTimeFilter!) {
  series(
    filter: {
      videogame: { id: "counter-strike-2" }
      scheduledAt: $dateRange
      opponents: [{ teamId: $team1Id }, { teamId: $team2Id }]
    }
    first: 50
  ) {
    edges {
      node {
        id
        name
        scheduledAt
        state
        bestOf
        winnerId
        opponents {
          id
          score
          team {
            id
            name
          }
        }
        games {
          id
          map {
            id
            name
          }
          opponents {
            score
            team {
              id
              name
            }
          }
        }
      }
    }
  }
}
```

#### 5. **Map-Specific Performance** (VERIFIED SCHEMA)
```graphql
query GetMapStats($teamId: ID!, $mapId: ID!, $dateRange: DateTimeFilter!) {
  team(id: $teamId) {
    id
    name
    mapStats: stats(
      filter: {
        videogame: { id: "counter-strike-2" }
        period: $dateRange
        maps: [$mapId]
      }
    ) {
      ... on CounterStrike2TeamStats {
        gamesCount
        gamesWonCount
        winRate: gamesWonCount # Calculate: gamesWonCount / gamesCount
        roundsWonCount
        roundsLostCount
        roundWinRate # Calculate: roundsWonCount / (roundsWonCount + roundsLostCount)
        kills
        deaths
        kdr
        adr
        rating
        firstKills
        clutchesWon: clutches {
          won1v1
          won1v2
          won1v3
          won1v4
          won1v5
        }
      }
    }
  }
}
```

### **REAL-TIME DATA VALIDATION PROTOCOL:**
```
Before Each Analysis:
├── Verify API Response Status (200 OK)
├── Check Data Freshness (<24 hours for active tournaments)
├── Validate Required Fields Present (not null/undefined)
├── Confirm Game Count ≥ 5 for statistical significance
├── Cross-reference Team/Player IDs exist in response
├── Ensure Date Range Covers Requested Period
└── Flag Any Mock/Test Data Indicators

Data Quality Gates:
├── Minimum Games: 5 per team in analysis period
├── Complete Stats: All core metrics present
├── Fresh Data: Updated within last 24 hours
├── Valid Teams: Both teams have recent match history
└── API Health: Response time <5 seconds
```

---

## 2. **Enhanced Date-Based Analysis Framework**

### **Dynamic Date Range Calculation:**
```javascript
// Date calculation for analysis periods
const getAnalysisDateRanges = (selectedOption, customDates = null) => {
  const now = moment().utc();
  
  const ranges = {
    // Match analysis period
    matchDate: getMatchDateRange(selectedOption, customDates),
    
    // Team form analysis (last 30 days from match date)
    teamForm: {
      start: moment(matchDate.start).subtract(30, 'days').toISOString(),
      end: moment(matchDate.start).subtract(1, 'day').toISOString()
    },
    
    // Head-to-head history (last 12 months)
    headToHead: {
      start: moment(matchDate.start).subtract(12, 'months').toISOString(),
      end: moment(matchDate.start).subtract(1, 'day').toISOString()
    },
    
    // Player consistency (last 60 days)
    playerConsistency: {
      start: moment(matchDate.start).subtract(60, 'days').toISOString(),
      end: moment(matchDate.start).subtract(1, 'day').toISOString()
    }
  };
  
  return ranges;
};
```

### **Time-Zone Aware Match Scheduling:**
```graphql
# Example query with proper date filtering
query GetTodaysMatches {
  series(
    filter: {
      videogame: { id: "counter-strike-2" }
      scheduledAt: {
        gte: "2024-01-01T00:00:00.000Z"  # Today UTC start
        lte: "2024-01-01T23:59:59.999Z"  # Today UTC end
      }
      state: SCHEDULED
    }
    first: 50
  ) {
    edges {
      node {
        scheduledAt
        # ... rest of match data
      }
    }
  }
}
```

---

## 3. **Statistical Analysis Engine with Real Data Validation**

### **Team Dominance Score (TDS) - Real Data Only**
```javascript
const calculateTDS = async (teamId, analysisDate) => {
  // Get REAL team statistics from GRID API
  const teamStats = await gridAPI.getTeamStats(teamId, {
    start: moment(analysisDate).subtract(30, 'days').toISOString(),
    end: moment(analysisDate).subtract(1, 'day').toISOString()
  });
  
  // Validate data exists and is not simulated
  if (!teamStats || teamStats.gamesCount < 5) {
    throw new Error(`Insufficient real data for team ${teamId}`);
  }
  
  const metrics = {
    winRate: teamStats.gamesWonCount / teamStats.gamesCount,
    kdr: teamStats.kills / teamStats.deaths,
    adr: teamStats.adr,
    rating: teamStats.rating,
    headshotRate: teamStats.headshotPercentage / 100,
    clutchSuccess: calculateClutchRate(teamStats.clutches),
    firstKillRate: teamStats.firstKills / teamStats.roundsCount
  };
  
  // Calculate TDS with validated real data
  const tds = (
    (metrics.winRate * 0.25) +
    (normalizeKDR(metrics.kdr) * 0.20) +
    (normalizeADR(metrics.adr) * 0.15) +
    (normalizeRating(metrics.rating) * 0.15) +
    (metrics.headshotRate * 0.10) +
    (metrics.clutchSuccess * 0.10) +
    (metrics.firstKillRate * 0.05)
  ) * 100;
  
  return {
    score: tds,
    confidence: calculateConfidence(teamStats.gamesCount, dataFreshness),
    dataPoints: teamStats.gamesCount,
    lastUpdate: teamStats.lastUpdated
  };
};
```

### **Player Impact Rating (PIR) - Verified Real Data**
```javascript
const calculatePIR = async (playerId, analysisDate) => {
  const playerStats = await gridAPI.getPlayerStats(playerId, {
    start: moment(analysisDate).subtract(30, 'days').toISOString(),
    end: moment(analysisDate).subtract(1, 'day').toISOString()
  });
  
  // Strict validation - no mock data accepted
  if (!playerStats || 
      playerStats.gamesCount < 5 || 
      !playerStats.rating ||
      playerStats.dataSource === 'simulated') {
    return null; // Exclude from analysis
  }
  
  const pir = calculatePlayerImpact(playerStats);
  return {
    playerId,
    name: playerStats.name,
    rating: pir,
    gamesPlayed: playerStats.gamesCount,
    confidence: playerStats.gamesCount >= 10 ? 'HIGH' : 'MEDIUM'
  };
};
```

---

## 4. **Real-Time Risk Assessment Matrix**

| Risk Tier | TDS Gap | Recent Form | Data Quality | Games Sample | Confidence Threshold |
|-----------|---------|-------------|--------------|--------------|---------------------|
| **🛡️ LOW** | ≥20 points | ≥80% W/R (L10) | REAL data ≥95% | ≥15 games | 85%+ |
| **⚖️ MEDIUM** | ≥12 points | ≥65% W/R (L10) | REAL data ≥85% | ≥10 games | 75%+ |
| **⚠️ HIGH** | ≥5 points | ≥55% W/R (L10) | REAL data ≥75% | ≥7 games | 65%+ |
| **❌ EXCLUDE** | Any gap | Any form | <75% real data | <5 games | Any |

**Automatic Exclusions:**
- Any detection of simulated/mock data
- Insufficient game sample (<5 recent games)
- Stale data (>48 hours old during tournaments)
- Missing core statistics (K/D, ADR, Rating)
- API errors or incomplete responses

---

## 5. **Enhanced Output Format with Date Context**

### **Primary Prediction Structure:**
```
📅 ANALYSIS DATE: [Selected Date Option - TODAY/TOMORROW/CUSTOM]
🕐 Analysis Generated: [Current UTC Timestamp]
📊 Data Coverage: [Date Range Used for Statistics]

🏆 MATCH ANALYSIS: [Team A] vs [Team B]
📍 Match Details: [Tournament] | [Format] | [Scheduled Time UTC]
📈 Current Odds: [Team A: X.XX] | [Team B: X.XX] | Movement: [Direction/Time]

🎯 RECOMMENDED BET:
Bet: [Team/Market] @ [Odds] | Risk: [🛡️/⚖️/⚠️] | Confidence: [XX]%
Expected Value: [+X.X%] | Kelly Stake: [X.XX units] | Max Risk: [X%]

📊 REAL-TIME GRID API ANALYSIS:
├─ 🏅 Team Dominance Score: [Team A: XX.X] vs [Team B: XX.X]
│   └─ Data Points: [Team A: X games] | [Team B: X games]
├─ 💀 Performance Metrics (Last 30 Days):
│   ├─ K/D Ratio: [X.XX] vs [X.XX]
│   ├─ ADR: [XXX.X] vs [XXX.X] 
│   ├─ Rating: [X.XX] vs [X.XX]
│   └─ Win Rate: [XX.X%] vs [XX.X%]
├─ 🎯 Advanced Statistics:
│   ├─ Headshot %: [XX.X%] vs [XX.X%]
│   ├─ First Kill Rate: [XX.X%] vs [XX.X%]
│   ├─ Clutch Success: [XX.X%] vs [XX.X%]
│   └─ Multi-Kill Ability: [2K+: XX] vs [2K+: XX]
├─ 🧮 Player Impact Ratings:
│   ├─ Team A Top 3: [Player1: XX.X] [Player2: XX.X] [Player3: XX.X]
│   └─ Team B Top 3: [Player1: XX.X] [Player2: XX.X] [Player3: XX.X]
└─ 📈 Form Trend: [Team A: 📈/📉/➡️] | [Team B: 📈/📉/➡️]

🔍 HEAD-TO-HEAD (Last 12 Months):
├─ Series Record: [X-X] in favor of [Team]
├─ Map Record: [XX-XX] total maps played
├─ Average Score Differential: [+X.X rounds per map]
├─ Recent Encounters: [Last 3 results with dates]
└─ Key Matchup: [Top player vs Top player stats]

⚠️ DATA VALIDATION STATUS:
├─ ✅ Real Data Only: No simulated/mock data detected
├─ 📊 Data Completeness: [XX%] of required metrics available
├─ 🕐 Data Freshness: Last updated [X hours ago]
├─ 🎮 Sample Size: [Team A: X games] | [Team B: X games]
└─ 🔍 API Health: [Response time: XXXms] | [Rate limit: XX/40]

📅 TIME-SENSITIVE FACTORS:
├─ Days Since Last Match: [Team A: X days] | [Team B: X days]
├─ Tournament Context: [Group/Playoff stage impact]
├─ Schedule Intensity: [Games in last 7 days]
└─ Peak Performance Window: [Analysis of team momentum]

🔄 MONITORING SCHEDULE:
├─ Next Data Update: [XX:XX UTC]
├─ Pre-Match Update: [X hours before match]
├─ Key Metrics Watch: [Roster changes, odds movement]
└─ Live Adjustment Triggers: [Specified conditions]
```

### **Top Matches Summary with Date Context:**
```
📅 [DATE OPTION] CS2 BETTING OPPORTUNITIES

RANK │ MATCH TIME (UTC)     │ TEAMS              │ BET TYPE    │ ODDS │ EV   │ CONFIDENCE │
─────┼─────────────────────┼────────────────────┼─────────────┼──────┼──────┼────────────┤
  1  │ 14:00               │ NaVi vs G2         │ NaVi ML     │ 1.85 │ +8.2%│ 87% 🛡️     │
  2  │ 16:30               │ FaZe vs Astralis   │ Over 2.5    │ 2.10 │ +6.8%│ 82% 🛡️     │
  3  │ 19:00               │ Vitality vs MOUZ   │ Vitality-1.5│ 2.45 │ +5.9%│ 79% ⚖️     │
  4  │ 21:15               │ Spirit vs Liquid   │ Spirit ML   │ 1.75 │ +4.3%│ 76% ⚖️     │
  5  │ 23:45               │ ENCE vs BIG        │ Under 2.5   │ 1.95 │ +3.7%│ 71% ⚖️     │

📊 Total Expected Value: +28.9% | Combined Kelly: 4.8 units
⚠️ All predictions based on verified real-time GRID API data only
```

---

## 6. **Implementation Validation Checklist**

### **Pre-Analysis Validation:**
- [ ] **Date Selection Confirmed**: [TODAY/TOMORROW/CUSTOM] properly parsed
- [ ] **API Key Authenticated**: Valid response from GRID API
- [ ] **Match Data Retrieved**: Real matches found for selected date
- [ ] **No Mock Data**: Verified all statistics are from actual games
- [ ] **Minimum Sample Size**: Each team ≥5 games in analysis period
- [ ] **Data Freshness**: All data <24 hours old for active tournaments
- [ ] **Complete Statistics**: All required metrics present for analysis
- [ ] **Rate Limit Compliance**: Staying within API limits

### **Analysis Quality Gates:**
- [ ] **TDS Calculation**: Based on verified real statistics only
- [ ] **PIR Calculation**: Minimum 5 games per player
- [ ] **H2H Analysis**: Actual historical matchups only
- [ ] **Confidence Scoring**: Properly weighted by data quality
- [ ] **Risk Assessment**: Accurate tier classification
- [ ] **EV Calculation**: Based on real odds and probabilities
- [ ] **Kelly Sizing**: Proper bankroll management applied

### **Output Validation:**
- [ ] **Time Zones**: All times displayed in UTC
- [ ] **Data Sources**: Clearly attributed to GRID API
- [ ] **Limitations**: Any data gaps or uncertainties noted
- [ ] **Update Schedule**: Next refresh time specified
- [ ] **Error Handling**: Graceful degradation if API issues
- [ ] **Performance Tracking**: Results logged for model improvement

---

**🚨 ABSOLUTE REQUIREMENT: This analysis framework must only use real, live data from GRID API and verified sources. Any detection of mock, simulated, or test data must result in immediate exclusion from the analysis. All predictions must be backed by actual match statistics and current team performance data.**