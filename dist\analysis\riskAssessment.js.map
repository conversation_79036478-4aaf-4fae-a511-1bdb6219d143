{"version": 3, "file": "riskAssessment.js", "sourceRoot": "", "sources": ["../../src/analysis/riskAssessment.ts"], "names": [], "mappings": ";;;AAsBA,MAAa,cAAc;IA0CzB;;OAEG;IACH,MAAM,CAAC,cAAc,CACnB,QAA4B,EAC5B,QAA4B,EAC5B,UAA0D,EAC1D,WAAsF;QAGtF,uCAAuC;QACvC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,CAAC;YACxD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,0CAA0C;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEzD,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxD,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC;QAEpD,0BAA0B;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE3F,yBAAyB;QACzB,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3F,4DAA4D;QAC5D,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,aAAa,CACpB,MAAM,EACN,gBAAgB,EAChB,WAAW,CAAC,eAAe,EAC3B,aAAa,EACb,aAAa,EACb,OAAO,CAAC,QAAQ,CACjB,EAAE,CAAC;gBACF,OAAO,OAAO,CAAC,IAAI,CAAC;YACtB,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAC1B,QAA4B,EAC5B,QAA4B,EAC5B,WAAsF;QAGtF,2BAA2B;QAC3B,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oBAAoB;QACpB,IAAI,WAAW,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK;YAClC,QAAQ,CAAC,KAAK,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sBAAsB;QACtB,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAC1B,MAAc,EACd,iBAAyB,EACzB,kBAA0B,EAC1B,WAAmB,EACnB,eAAuB,EACvB,QAAsB;QAEtB,OAAO,CACL,MAAM,IAAI,QAAQ,CAAC,MAAM;YACzB,iBAAiB,IAAI,QAAQ,CAAC,iBAAiB;YAC/C,kBAAkB,IAAI,QAAQ,CAAC,kBAAkB;YACjD,WAAW,IAAI,QAAQ,CAAC,WAAW;YACnC,eAAe,IAAI,QAAQ,CAAC,mBAAmB,CAChD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,KAAsB,EAAE,KAAsB;QACjF,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,6BAA6B,CAClC,SAAiB,EACjB,SAAiB,EACjB,QAA4B,EAC5B,QAA4B,EAC5B,IAAsC,EACtC,QAAkB;QAGlB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QACxD,MAAM,YAAY,GAAG,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QAC7D,MAAM,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC1D,MAAM,eAAe,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAElE,oDAAoD;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzD,MAAM,eAAe,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,iCAAiC;QAC/E,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,aAAa;QAE1E,6BAA6B;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEtF,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAEzG,wBAAwB;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAEnG,mCAAmC;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEjD,OAAO;YACL,GAAG,EAAE,GAAG,YAAY,KAAK;YACzB,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YACvC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,GAAG,EAAE;YAClD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;YAC9C,OAAO,EAAE,OAAO;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,QAA4B,EAC5B,QAA4B,EAC5B,QAAkB;QAElB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1D,MAAM,aAAa,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAE1C,4BAA4B;QAC5B,MAAM,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QAExE,OAAO,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,UAA2B;QAC1D,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;QACjD,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,WAAmB,EAAE,IAAY,EAAE,UAAkB;QACzF,MAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QAC7D,OAAO,CAAC,mBAAmB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,WAAmB,EAAE,IAAY,EAAE,UAAkB;QACtF,MAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;QACnB,MAAM,CAAC,GAAG,mBAAmB,CAAC;QAC9B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEhB,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAEtC,2CAA2C;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,aAAa;QACpC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,IAAc;QAC7C,MAAM,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QAC5D,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,IAAc;QACzC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,QAA4B,EAC5B,QAA4B,EAC5B,WAAsF;QAEtF,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,WAAW,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACzE,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;;AAzSH,wCA0SC;AAxSC,uDAAuD;AAC/B,yBAAU,GAAyB;IACzD;QACE,IAAI,EAAE,KAAK;QACX,QAAQ,EAAE;YACR,MAAM,EAAE,EAAE;YACV,iBAAiB,EAAE,IAAI;YACvB,kBAAkB,EAAE,EAAE;YACtB,WAAW,EAAE,EAAE;YACf,mBAAmB,EAAE,EAAE;SACxB;QACD,WAAW,EAAE,+CAA+C;QAC5D,KAAK,EAAE,KAAK;KACb;IACD;QACE,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE;YACR,MAAM,EAAE,EAAE;YACV,iBAAiB,EAAE,IAAI;YACvB,kBAAkB,EAAE,EAAE;YACtB,WAAW,EAAE,EAAE;YACf,mBAAmB,EAAE,EAAE;SACxB;QACD,WAAW,EAAE,2CAA2C;QACxD,KAAK,EAAE,IAAI;KACZ;IACD;QACE,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE;YACR,MAAM,EAAE,CAAC;YACT,iBAAiB,EAAE,IAAI;YACvB,kBAAkB,EAAE,EAAE;YACtB,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE,EAAE;SACxB;QACD,WAAW,EAAE,uCAAuC;QACpD,KAAK,EAAE,IAAI;KACZ;CACF,CAAC"}