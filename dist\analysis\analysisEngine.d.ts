import { MatchAnalysis, AnalysisConfig, DateSelection, DateRange } from '../types';
export declare class AnalysisEngine {
    private gridClient;
    private config;
    constructor(apiKey: string, config: AnalysisConfig);
    analyzeMatches(dateSelection: DateSelection, customDates?: DateRange): Promise<MatchAnalysis[]>;
    private analyzeSingleMatch;
    private getPlayerAnalysis;
    private getMatchTitle;
    updateConfig(newConfig: Partial<AnalysisConfig>): void;
    getConfig(): AnalysisConfig;
    validateConfig(): {
        isValid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=analysisEngine.d.ts.map