import { MatchAnalysis, AnalysisConfig, DateSelection, DateRange } from '../types';
export declare class AnalysisEngine {
    private gridClient;
    private config;
    constructor(apiKey: string, config: AnalysisConfig);
    /**
     * Analyze matches for the specified date selection
     */
    analyzeMatches(dateSelection: DateSelection, customDates?: DateRange): Promise<MatchAnalysis[]>;
    /**
     * Analyze a single match
     */
    private analyzeSingleMatch;
    /**
     * Get player analysis for both teams
     */
    private getPlayerAnalysis;
    /**
     * Get match title for logging
     */
    private getMatchTitle;
    /**
     * Update analysis configuration
     */
    updateConfig(newConfig: Partial<AnalysisConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): AnalysisConfig;
    /**
     * Validate configuration
     */
    validateConfig(): {
        isValid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=analysisEngine.d.ts.map