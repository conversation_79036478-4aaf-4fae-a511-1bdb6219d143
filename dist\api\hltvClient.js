"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HLTVClient = void 0;
const axios_1 = __importDefault(require("axios"));
const cheerio = __importStar(require("cheerio"));
class HLTVClient {
    constructor() {
        this.requestCount = 0;
        this.lastRequestTime = 0;
        this.RATE_LIMIT = 20;
        this.baseURL = 'https://www.hltv.org';
        this.client = axios_1.default.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        });
        this.client.interceptors.request.use(async (config) => {
            await this.enforceRateLimit();
            return config;
        });
        this.client.interceptors.response.use((response) => response, (error) => {
            console.error(`HLTV Scraping Error: ${error.message}`);
            throw new Error(`HLTV scraping failed: ${error.message}`);
        });
    }
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = (60 * 1000) / this.RATE_LIMIT;
        if (timeSinceLastRequest < minInterval) {
            const waitTime = minInterval - timeSinceLastRequest;
            console.log(`⏱️ Rate limiting: waiting ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        this.requestCount++;
        this.lastRequestTime = Date.now();
    }
    async makeRequest(endpoint) {
        try {
            console.log(`🔍 HLTV Scraping Request: ${endpoint}`);
            const startTime = Date.now();
            const response = await this.client.get(endpoint);
            const duration = Date.now() - startTime;
            console.log(`✅ HLTV Scraping Request completed in ${duration}ms`);
            return response.data;
        }
        catch (error) {
            console.error(`❌ HLTV Scraping Error for ${endpoint}:`, error);
            throw error;
        }
    }
    parseHTML(html) {
        return cheerio.load(html);
    }
    async getUpcomingMatches() {
        console.log(`🔍 Fetching upcoming CS2 matches from HLTV`);
        try {
            const html = await this.makeRequest('/matches');
            const $ = this.parseHTML(html);
            const matches = [];
            $('.upcomingMatch').each((index, element) => {
                try {
                    const $match = $(element);
                    const matchId = $match.find('a').attr('href')?.split('/')[2] || '';
                    const team1Name = $match.find('.matchTeam').first().find('.matchTeamName').text().trim();
                    const team2Name = $match.find('.matchTeam').last().find('.matchTeamName').text().trim();
                    const eventName = $match.find('.matchEvent').text().trim();
                    const timeElement = $match.find('.matchTime');
                    const matchTime = timeElement.attr('data-unix') ?
                        new Date(parseInt(timeElement.attr('data-unix')) * 1000).toISOString() :
                        new Date().toISOString();
                    if (matchId && team1Name && team2Name) {
                        matches.push({
                            id: matchId,
                            team1: { id: '', name: team1Name },
                            team2: { id: '', name: team2Name },
                            event: eventName,
                            date: matchTime,
                            format: 'BO3',
                            status: 'upcoming'
                        });
                    }
                }
                catch (parseError) {
                    console.warn(`Failed to parse match ${index}:`, parseError);
                }
            });
            console.log(`✅ Found ${matches.length} upcoming matches from HLTV`);
            return matches;
        }
        catch (error) {
            console.error(`❌ Failed to get upcoming matches:`, error);
            throw new Error(`REAL DATA ONLY: Cannot retrieve upcoming matches from HLTV. Error: ${error}. No mock data will be used.`);
        }
    }
    async searchPlayers(playerName) {
        console.log(`🔍 Searching for player: ${playerName}`);
        try {
            const html = await this.makeRequest(`/search?query=${encodeURIComponent(playerName)}`);
            const $ = this.parseHTML(html);
            const players = [];
            $('.search-result').each((index, element) => {
                try {
                    const $result = $(element);
                    const playerLink = $result.find('a').attr('href');
                    if (playerLink && playerLink.includes('/player/')) {
                        const playerId = playerLink.split('/')[2];
                        const playerName = $result.find('.search-result-name').text().trim();
                        const team = $result.find('.search-result-team').text().trim();
                        const country = $result.find('.flag').attr('title') || '';
                        players.push({
                            id: playerId,
                            name: playerName,
                            team: team || undefined,
                            country: country || undefined
                        });
                    }
                }
                catch (parseError) {
                    console.warn(`Failed to parse player result ${index}:`, parseError);
                }
            });
            console.log(`✅ Found ${players.length} players for "${playerName}"`);
            return players;
        }
        catch (error) {
            console.error(`❌ Failed to search players for "${playerName}":`, error);
            throw new Error(`REAL DATA ONLY: Cannot search for player "${playerName}". HLTV Error: ${error}. No mock data will be used.`);
        }
    }
    async getPlayerProfile(playerId) {
        console.log(`🔍 Fetching player profile for ID: ${playerId}`);
        try {
            const html = await this.makeRequest(`/player/${playerId}/-`);
            const $ = this.parseHTML(html);
            const name = $('.playerNickname').text().trim();
            const realName = $('.playerRealname').text().trim();
            const country = $('.playerCountry .flag').attr('title') || '';
            const team = $('.playerTeam .text-ellipsis').text().trim();
            const age = $('.playerAge .bold').text().trim();
            const player = {
                id: playerId,
                name: name || realName,
                nickname: name !== realName ? name : undefined,
                country: country || undefined,
                team: team || undefined,
                age: age ? parseInt(age) : undefined
            };
            console.log(`✅ Retrieved player profile: ${player.name}`);
            return player;
        }
        catch (error) {
            console.error(`❌ Failed to get player profile for ${playerId}:`, error);
            throw new Error(`REAL DATA ONLY: Cannot retrieve player profile for ${playerId}. HLTV Error: ${error}. No mock data will be used.`);
        }
    }
    async getPlayerStats(playerId) {
        console.log(`🔍 Fetching player statistics for ID: ${playerId}`);
        try {
            const html = await this.makeRequest(`/player/${playerId}/-`);
            const $ = this.parseHTML(html);
            const stats = {
                rating: 0,
                kills: 0,
                deaths: 0,
                assists: 0,
                kd_ratio: 0,
                adr: 0,
                headshot_percentage: 0,
                maps_played: 0,
                rounds_played: 0,
                first_kills: 0,
                first_deaths: 0,
                clutches_won: 0,
                clutches_total: 0
            };
            $('.stats-row').each((index, element) => {
                const $row = $(element);
                const statName = $row.find('.stat-name').text().trim().toLowerCase();
                const statValue = parseFloat($row.find('.stat-value').text().trim()) || 0;
                switch (statName) {
                    case 'rating 2.0':
                        stats.rating = statValue;
                        break;
                    case 'kills':
                        stats.kills = statValue;
                        break;
                    case 'deaths':
                        stats.deaths = statValue;
                        break;
                    case 'assists':
                        stats.assists = statValue;
                        break;
                    case 'k/d ratio':
                        stats.kd_ratio = statValue;
                        break;
                    case 'adr':
                        stats.adr = statValue;
                        break;
                    case 'headshot %':
                        stats.headshot_percentage = statValue;
                        break;
                    case 'maps played':
                        stats.maps_played = statValue;
                        break;
                    case 'rounds played':
                        stats.rounds_played = statValue;
                        break;
                }
            });
            console.log(`✅ Retrieved player statistics for player ${playerId} (Rating: ${stats.rating})`);
            return stats;
        }
        catch (error) {
            console.error(`❌ Failed to get player stats for ${playerId}:`, error);
            throw new Error(`REAL DATA ONLY: Cannot retrieve player statistics for ${playerId}. HLTV Error: ${error}. No mock data will be used.`);
        }
    }
    async getPlayerCareerStats(playerId) {
        console.log(`🔍 Fetching player career statistics for ID: ${playerId}`);
        try {
            const hltvStats = await this.getPlayerStats(playerId);
            console.log(`✅ Retrieved player career statistics for player ${playerId}`);
            return this.transformPlayerStats(hltvStats);
        }
        catch (error) {
            console.error(`❌ Failed to get player career stats for ${playerId}:`, error);
            throw new Error(`REAL DATA ONLY: Cannot retrieve player career statistics for ${playerId}. HLTV Error: ${error}. No mock data will be used.`);
        }
    }
    async getTeamInfo(teamName) {
        console.log(`🔍 Searching for team: ${teamName}`);
        try {
            const html = await this.makeRequest(`/search?query=${encodeURIComponent(teamName)}`);
            const $ = this.parseHTML(html);
            let teamInfo = null;
            $('.search-result').each((index, element) => {
                const $result = $(element);
                const teamLink = $result.find('a').attr('href');
                if (teamLink && teamLink.includes('/team/')) {
                    const teamId = teamLink.split('/')[2];
                    const name = $result.find('.search-result-name').text().trim();
                    const country = $result.find('.flag').attr('title') || '';
                    if (name.toLowerCase().includes(teamName.toLowerCase())) {
                        teamInfo = {
                            id: teamId,
                            name: name,
                            country: country || undefined
                        };
                        return false;
                    }
                }
            });
            if (teamInfo) {
                console.log(`✅ Found team: ${teamInfo.name}`);
            }
            else {
                console.log(`⚠️ Team "${teamName}" not found`);
            }
            return teamInfo;
        }
        catch (error) {
            console.error(`❌ Failed to search for team "${teamName}":`, error);
            throw new Error(`REAL DATA ONLY: Cannot search for team "${teamName}". HLTV Error: ${error}. No mock data will be used.`);
        }
    }
    transformPlayerStats(hltvStats) {
        const winRate = hltvStats.win_rate || 0.5;
        return {
            gamesCount: hltvStats.maps_played || 0,
            gamesWonCount: Math.floor((hltvStats.maps_played || 0) * winRate),
            gamesLostCount: Math.floor((hltvStats.maps_played || 0) * (1 - winRate)),
            roundsCount: hltvStats.rounds_played || 0,
            roundsWonCount: Math.floor((hltvStats.rounds_played || 0) * winRate),
            roundsLostCount: Math.floor((hltvStats.rounds_played || 0) * (1 - winRate)),
            kills: hltvStats.kills || 0,
            deaths: hltvStats.deaths || 0,
            assists: hltvStats.assists || 0,
            headshots: Math.floor((hltvStats.kills || 0) * (hltvStats.headshot_percentage || 0) / 100),
            headshotPercentage: hltvStats.headshot_percentage || 0,
            kdr: hltvStats.kd_ratio || 0,
            adr: hltvStats.adr || 0,
            rating: hltvStats.rating2 || hltvStats.rating || 0,
            firstKills: hltvStats.first_kills || 0,
            firstDeaths: hltvStats.first_deaths || 0,
            clutchesWon: hltvStats.clutches_won || 0,
            clutchesTotal: hltvStats.clutches_total || 0,
            multiKills: {
                k2: hltvStats.multi_kills?.k2 || 0,
                k3: hltvStats.multi_kills?.k3 || 0,
                k4: hltvStats.multi_kills?.k4 || 0,
                k5: hltvStats.multi_kills?.k5 || 0
            },
            maps: []
        };
    }
    getAPIHealth() {
        return {
            responseTime: this.lastRequestTime > 0 ? Date.now() - this.lastRequestTime : 0,
            rateLimit: `${this.requestCount}/${this.RATE_LIMIT}`,
            requestCount: this.requestCount
        };
    }
    async validateConnection() {
        try {
            console.log('🔍 Validating HLTV connection...');
            const html = await this.makeRequest('/');
            if (html.includes('hltv') || html.includes('HLTV')) {
                console.log('✅ HLTV connection validated successfully');
                return true;
            }
            else {
                console.error('❌ HLTV connection validation failed: Invalid response');
                return false;
            }
        }
        catch (error) {
            console.error('❌ HLTV connection validation failed:', error);
            return false;
        }
    }
}
exports.HLTVClient = HLTVClient;
//# sourceMappingURL=hltvClient.js.map