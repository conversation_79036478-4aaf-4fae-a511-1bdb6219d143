/**
 * CS2 Betting Analysis Framework - Quick Start Example
 * 
 * This example shows how to use the framework programmatically
 * for integration into other applications or services.
 */

import { 
  AnalysisEngine, 
  GridAPIClient, 
  StatisticalEngine,
  RiskAssessment,
  analyzeCS2Matches 
} from '../src';

// GRID API Key
const API_KEY = 'cDXLTwMg735stCkxIaaAEdlkYlkKcu88dwjshjYn';

async function quickStartExample() {
  console.log('🚀 CS2 Betting Analysis - Quick Start Example\n');

  try {
    // Method 1: Simple Analysis Function
    console.log('📊 Method 1: Simple Analysis');
    const summary = await analyzeCS2Matches(API_KEY, 'TODAY', {
      confidenceThreshold: 75,
      maxRiskTier: 'MEDIUM'
    });
    console.log(summary);
    console.log('\n');

    // Method 2: Direct API Client Usage
    console.log('🔧 Method 2: Direct API Client');
    const client = new GridAPIClient(API_KEY);
    
    // Test connection
    const isConnected = await client.validateConnection();
    console.log(`API Connected: ${isConnected}`);
    
    // Get teams
    const teams = await client.getCS2Teams('FaZe');
    console.log(`Found ${teams.edges.length} teams matching 'FaZe'`);
    console.log('\n');

    // Method 3: Full Analysis Engine
    console.log('⚙️ Method 3: Full Analysis Engine');
    const engine = new AnalysisEngine(API_KEY, {
      dateSelection: 'TODAY',
      minGamesThreshold: 5,
      confidenceThreshold: 70,
      maxRiskTier: 'HIGH',
      includePlayerAnalysis: true,
      includeMapAnalysis: false
    });

    // Validate configuration
    const validation = engine.validateConfig();
    console.log(`Configuration valid: ${validation.isValid}`);
    
    // Run analysis (would work with correct match schema)
    // const analyses = await engine.analyzeMatches('TODAY');
    console.log('Analysis engine ready for live match data');
    console.log('\n');

    // Method 4: Individual Component Usage
    console.log('🧮 Method 4: Individual Components');
    
    // Example team statistics (would come from API)
    const mockStats = {
      gamesCount: 10,
      gamesWonCount: 7,
      gamesLostCount: 3,
      seriesCount: 5,
      seriesWonCount: 4,
      seriesLostCount: 1,
      roundsCount: 160,
      roundsWonCount: 95,
      roundsLostCount: 65,
      kills: 1200,
      deaths: 980,
      assists: 600,
      headshots: 480,
      headshotPercentage: 40.0,
      kdr: 1.22,
      adr: 85.5,
      rating: 1.18,
      firstKills: 65,
      firstDeaths: 52,
      multiKills: { k2: 35, k3: 8, k4: 2, k5: 0 },
      clutches: {
        won1v1: 6, won1v2: 2, won1v3: 1, won1v4: 0, won1v5: 0,
        total1v1: 10, total1v2: 5, total1v3: 2, total1v4: 0, total1v5: 0
      }
    };

    // Calculate Team Dominance Score
    const tds = StatisticalEngine.calculateTDS(mockStats, 'Example Team');
    console.log(`Team Dominance Score: ${tds.score} (${tds.confidence} confidence)`);
    
    // Calculate expected value
    const ev = StatisticalEngine.calculateExpectedValue(0.65, 1.85, 80);
    console.log(`Expected Value: ${ev}%`);
    
    // Calculate Kelly stake
    const kelly = StatisticalEngine.calculateKellyStake(0.65, 1.85, 80);
    console.log(`Kelly Stake: ${kelly} units`);

    console.log('\n✅ Quick start example completed successfully!');

  } catch (error) {
    console.error('❌ Error in quick start example:', error);
  }
}

// Integration example for web applications
export class CS2BettingService {
  private engine: AnalysisEngine;

  constructor(apiKey: string) {
    this.engine = new AnalysisEngine(apiKey, {
      dateSelection: 'TODAY',
      minGamesThreshold: 5,
      confidenceThreshold: 75,
      maxRiskTier: 'MEDIUM',
      includePlayerAnalysis: true,
      includeMapAnalysis: false
    });
  }

  async getTodaysOpportunities() {
    try {
      const analyses = await this.engine.analyzeMatches('TODAY');
      return analyses.filter(a => a.recommendation.expectedValue > 0);
    } catch (error) {
      console.error('Failed to get opportunities:', error);
      return [];
    }
  }

  async getTeamAnalysis(teamName: string) {
    // This would fetch and analyze specific team data
    // Implementation depends on available API endpoints
    return {
      team: teamName,
      status: 'Analysis would be implemented with correct API schema'
    };
  }

  async validateApiHealth() {
    return await this.engine['gridClient'].validateConnection();
  }
}

// Example usage in Express.js API
export function createExpressRoutes(app: any, apiKey: string) {
  const service = new CS2BettingService(apiKey);

  app.get('/api/cs2/opportunities', async (req: any, res: any) => {
    try {
      const opportunities = await service.getTodaysOpportunities();
      res.json({ success: true, data: opportunities });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  app.get('/api/cs2/health', async (req: any, res: any) => {
    try {
      const isHealthy = await service.validateApiHealth();
      res.json({ success: true, healthy: isHealthy });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  });
}

// Run example if called directly
if (require.main === module) {
  quickStartExample();
}
