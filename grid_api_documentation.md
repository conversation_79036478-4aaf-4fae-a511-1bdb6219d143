# GRID API - CS2 Data Collection Guide

## Quick Start for CS2 Data Collection

### Authentication Setup
```http
x-api-key: YOUR_API_KEY
```

### Rate Limits (Important for Data Collection)
- **Closed Platform**: 40 requests/minute
- **Open Platform**: 20 requests/minute
- **Strategy**: Batch requests and implement delays between calls

## Core CS2 Data Queries

### 1. Getting Match Series Data
```graphql
query GetCS2Series {
  allSeries(
    first: 50
    filter: {
      startTimeScheduled: {
        gte: "2024-01-01T00:00:00Z"
        lte: "2024-12-31T23:59:59Z"
      }
    }
    orderBy: StartTimeScheduled
  ) {
    totalCount
    pageInfo {
      hasNextPage
      endCursor
    }
    edges {
      node {
        id
        startTimeScheduled
        title { name }
        tournament { name }
        teams {
          name
          players { 
            name 
            gamertag
          }
        }
      }
    }
  }
}
```

### 2. Player Performance Data Collection
Key fields for CS2 player analysis:

#### Individual Game Stats (`PlayerGameStatisticsCs2`)
**Combat Performance:**
- `kills` - Total kills per game
- `deaths` - Total deaths per game  
- `killAssistsGiven` - Assists provided to teammates
- `headshots` - Headshot count (accuracy indicator)
- `firstKill` - First blood statistics
- `damageDealt` - Total damage output
- `won` - Win/loss record

**Economic Performance:**
- `money` - In-game economy management
- `inventoryValue` - Equipment value
- `netWorth` - Overall economic standing

#### Series Aggregates (`Cs2PlayerSeriesStatistics`)
**Long-term Performance:**
- `count` - Number of games played
- `kills.sum` - Total kills across series
- `deaths.sum` - Total deaths across series
- `kills.average` - Average kills per game
- `won` - Series win rate

### 3. Team Performance Data Collection

#### Team Game Stats (`TeamGameStatisticsCs2`)
**Team Combat:**
- `score` - Round/map scores
- `kills` - Team total kills
- `duration` - Match duration
- `won` - Win statistics
- `objectives` - Objective completions (bomb plants, defuses, etc.)

**Team Economics:**
- `money` - Team economy aggregates
- `netWorth` - Combined team wealth

#### Team vs Team Comparisons (`GameTeamsStatisticsByGameCs2`)
**Head-to-Head Analysis:**
- `kills` vs opponent kills
- `score` comparisons
- `damageDealt` vs `damageTaken`
- Economic differentials

## Data Collection Strategies

### 1. Historical Data Collection
```graphql
# Collect last 6 months of data
query GetRecentCS2Data {
  allSeries(
    first: 50
    filter: {
      startTimeScheduled: {
        gte: "2024-06-01T00:00:00Z"
        lte: "2024-12-01T00:00:00Z"
      }
    }
  ) {
    edges {
      node {
        id
        games {
          playerStatistics {
            player { gamertag }
            kills { sum, average }
            deaths { sum, average }
            headshots { sum }
            won { occurrences }
          }
        }
      }
    }
  }
}
```

### 2. Live/Recent Match Data
```graphql
# Get matches from last 24 hours
query GetRecentMatches {
  allSeries(
    first: 20
    filter: {
      startTimeScheduled: {
        gte: "2024-12-01T00:00:00Z"
      }
    }
    orderBy: StartTimeScheduled
  ) {
    edges {
      node {
        id
        startTimeScheduled
        games {
          teamStatistics {
            team { name }
            score { sum }
            kills { sum }
            won { occurrences }
          }
        }
      }
    }
  }
}
```

### 3. Player Rankings Data
```graphql
# Get top performing players
query GetTopPlayers {
  # Query multiple series to aggregate player performance
  allSeries(
    first: 50
    filter: {
      # Last 3 months for current form
      startTimeScheduled: {
        gte: "2024-09-01T00:00:00Z"
      }
    }
  ) {
    edges {
      node {
        games {
          playerStatistics {
            player { 
              gamertag 
              team { name }
            }
            kills { average, sum }
            deaths { average }
            headshots { sum }
            won { percentage }
            damageDealt { average }
          }
        }
      }
    }
  }
}
```

## Key Statistics for CS2 Analysis

### Player Performance Metrics
1. **K/D Ratio**: `kills.average / deaths.average`
2. **Headshot Percentage**: `headshots.sum / kills.sum * 100`
3. **ADR (Average Damage per Round)**: `damageDealt.average`
4. **Win Rate**: `won.percentage`
5. **First Kill Rate**: `firstKill` occurrences
6. **Economic Efficiency**: `kills.sum / money.average`

### Team Performance Metrics
1. **Round Win Rate**: `won.percentage`
2. **Team Kills per Game**: `kills.average`
3. **Objective Success**: `objectives` completion rate
4. **Economic Management**: `money` vs `inventoryValue` efficiency
5. **Match Duration**: `duration` analysis

## Pagination for Large Data Sets

### Collecting All Historical Data
```graphql
query GetAllCS2Data($after: String) {
  allSeries(
    first: 50
    after: $after
    filter: {
      startTimeScheduled: {
        gte: "2024-01-01T00:00:00Z"
      }
    }
  ) {
    totalCount
    pageInfo {
      hasNextPage
      endCursor
    }
    edges {
      cursor
      node {
        # Your data fields here
      }
    }
  }
}
```

**Collection Loop Strategy:**
1. Start with no `after` parameter
2. Extract `endCursor` from response
3. Use `endCursor` as `after` parameter for next request
4. Continue until `hasNextPage` is false
5. Respect rate limits between requests

## Common Data Collection Errors

### Rate Limiting
**Error**: `TOO_MANY_REQUESTS` (429)
**Solution**: Implement 2-3 second delays between requests

### Invalid Time Ranges
**Error**: `INVALID_ARGUMENT` - end date must be after start date
**Solution**: Always validate date ranges before queries

### Missing Player/Team Data
**Error**: `Invalid {playerId/teamId} value provided`
**Solution**: Verify entity IDs exist before filtering

## Practical Data Collection Tips

### 1. Batch Processing
- Collect 50 series per request (maximum)
- Process data in chunks to avoid memory issues
- Store intermediate results to handle interruptions

### 2. Time-based Collection
- **Daily**: Last 24 hours of matches
- **Weekly**: Last 7 days for trend analysis  
- **Monthly**: Last 30 days for ranking calculations
- **Historical**: 6-12 months for comprehensive analysis

### 3. Data Quality
- Filter out incomplete matches
- Verify player/team name consistency
- Handle missing statistics gracefully
- Cross-reference with tournament data

### 4. Performance Optimization
- Use specific field selections (don't query unnecessary data)
- Implement local caching for repeated queries
- Monitor API usage against rate limits
- Use compression for large data transfers

## Sample Data Collection Workflow

1. **Authentication**: Set up API key
2. **Tournament Discovery**: Find active CS2 tournaments
3. **Series Collection**: Gather match series data
4. **Player Stats**: Extract individual performance metrics
5. **Team Stats**: Aggregate team performance data
6. **Analysis**: Calculate derived metrics (K/D, win rates, etc.)
7. **Storage**: Save processed data for analysis
8. **Updates**: Regular incremental updates for new matches