export { AnalysisEngine } from './analysis/analysisEngine';
export { StatisticalEngine } from './analysis/statisticalEngine';
export { RiskAssessment } from './analysis/riskAssessment';
export { HeadToHeadAnalyzer } from './analysis/headToHeadAnalysis';
export { GridAPIClient } from './api/gridClient';
export { DateManager } from './utils/dateUtils';
export { PredictionFormatter } from './output/predictionFormatter';
export * from './types';
import { AnalysisEngine } from './analysis/analysisEngine';
import { PredictionFormatter } from './output/predictionFormatter';
import { DateSelection, AnalysisConfig } from './types';
export declare function analyzeCS2Matches(apiKey: string, dateSelection?: DateSelection, options?: Partial<AnalysisConfig>): Promise<string>;
export declare function getDetailedAnalysis(apiKey: string, dateSelection?: DateSelection, customDates?: {
    start: string;
    end: string;
}): Promise<string[]>;
declare const _default: {
    analyzeCS2Matches: typeof analyzeCS2Matches;
    getDetailedAnalysis: typeof getDetailedAnalysis;
    AnalysisEngine: typeof AnalysisEngine;
    PredictionFormatter: typeof PredictionFormatter;
};
export default _default;
//# sourceMappingURL=index.d.ts.map