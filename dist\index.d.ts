/**
 * CS2 Betting Analysis Framework
 * Enhanced Prediction with GRID API Integration
 *
 * This is the main entry point for the CS2 betting analysis system.
 * It provides programmatic access to the analysis engine for integration
 * with other systems or custom implementations.
 */
export { AnalysisEngine } from './analysis/analysisEngine';
export { StatisticalEngine } from './analysis/statisticalEngine';
export { RiskAssessment } from './analysis/riskAssessment';
export { HeadToHeadAnalyzer } from './analysis/headToHeadAnalysis';
export { GridAPIClient } from './api/gridClient';
export { DateManager } from './utils/dateUtils';
export { PredictionFormatter } from './output/predictionFormatter';
export * from './types';
import { AnalysisEngine } from './analysis/analysisEngine';
import { PredictionFormatter } from './output/predictionFormatter';
import { DateSelection, AnalysisConfig } from './types';
/**
 * Quick analysis function for easy integration
 */
export declare function analyzeCS2Matches(apiKey: string, dateSelection?: DateSelection, options?: Partial<AnalysisConfig>): Promise<string>;
/**
 * Get detailed analysis for a specific date range
 */
export declare function getDetailedAnalysis(apiKey: string, dateSelection?: DateSelection, customDates?: {
    start: string;
    end: string;
}): Promise<string[]>;
declare const _default: {
    analyzeCS2Matches: typeof analyzeCS2Matches;
    getDetailedAnalysis: typeof getDetailedAnalysis;
    AnalysisEngine: typeof AnalysisEngine;
    PredictionFormatter: typeof PredictionFormatter;
};
export default _default;
//# sourceMappingURL=index.d.ts.map