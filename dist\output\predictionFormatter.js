"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PredictionFormatter = void 0;
const dateUtils_1 = require("../utils/dateUtils");
class PredictionFormatter {
    /**
     * Format complete match analysis according to prompt specifications
     */
    static formatMatchAnalysis(analysis, dateSelection) {
        const lines = [];
        // Header with date context
        lines.push(`📅 ANALYSIS DATE: ${dateSelection}`);
        lines.push(`🕐 Analysis Generated: ${dateUtils_1.DateManager.getCurrentUTC()}`);
        lines.push(`📊 Data Coverage: Last 30 days for team form`);
        lines.push('');
        // Match details
        lines.push(`🏆 MATCH ANALYSIS: ${analysis.team1} vs ${analysis.team2}`);
        lines.push(`📍 Match Details: ${analysis.tournament} | ${analysis.format} | ${dateUtils_1.DateManager.formatDisplayDate(analysis.scheduledTime)}`);
        lines.push(`📈 Current Odds: [${analysis.team1}: ${analysis.currentOdds.team1}] | [${analysis.team2}: ${analysis.currentOdds.team2}] | Movement: ${analysis.currentOdds.movement}`);
        lines.push('');
        // Betting recommendation
        const riskEmoji = this.getRiskEmoji(analysis.recommendation.risk);
        lines.push(`🎯 RECOMMENDED BET:`);
        lines.push(`Bet: ${analysis.recommendation.bet} @ ${analysis.recommendation.odds} | Risk: ${riskEmoji} | Confidence: ${analysis.recommendation.confidence}%`);
        lines.push(`Expected Value: ${analysis.recommendation.expectedValue > 0 ? '+' : ''}${analysis.recommendation.expectedValue}% | Kelly Stake: ${analysis.recommendation.kellyStake} units | Max Risk: ${analysis.recommendation.maxRisk}%`);
        lines.push('');
        // Real-time GRID API analysis
        lines.push(`📊 REAL-TIME GRID API ANALYSIS:`);
        lines.push(`├─ 🏅 Team Dominance Score: [${analysis.team1}: ${analysis.teamDominanceScores.team1.score}] vs [${analysis.team2}: ${analysis.teamDominanceScores.team2.score}]`);
        lines.push(`│   └─ Data Points: [${analysis.team1}: ${analysis.teamDominanceScores.team1.dataPoints} games] | [${analysis.team2}: ${analysis.teamDominanceScores.team2.dataPoints} games]`);
        // Performance metrics
        const team1Metrics = analysis.teamDominanceScores.team1.metrics;
        const team2Metrics = analysis.teamDominanceScores.team2.metrics;
        lines.push(`├─ 💀 Performance Metrics (Last 30 Days):`);
        lines.push(`│   ├─ K/D Ratio: ${team1Metrics.kdr.toFixed(2)} vs ${team2Metrics.kdr.toFixed(2)}`);
        lines.push(`│   ├─ ADR: ${team1Metrics.adr.toFixed(1)} vs ${team2Metrics.adr.toFixed(1)}`);
        lines.push(`│   ├─ Rating: ${team1Metrics.rating.toFixed(2)} vs ${team2Metrics.rating.toFixed(2)}`);
        lines.push(`│   └─ Win Rate: ${(team1Metrics.winRate * 100).toFixed(1)}% vs ${(team2Metrics.winRate * 100).toFixed(1)}%`);
        // Advanced statistics
        lines.push(`├─ 🎯 Advanced Statistics:`);
        lines.push(`│   ├─ Headshot %: ${(team1Metrics.headshotRate * 100).toFixed(1)}% vs ${(team2Metrics.headshotRate * 100).toFixed(1)}%`);
        lines.push(`│   ├─ First Kill Rate: ${(team1Metrics.firstKillRate * 100).toFixed(1)}% vs ${(team2Metrics.firstKillRate * 100).toFixed(1)}%`);
        lines.push(`│   ├─ Clutch Success: ${(team1Metrics.clutchSuccess * 100).toFixed(1)}% vs ${(team2Metrics.clutchSuccess * 100).toFixed(1)}%`);
        lines.push(`│   └─ Multi-Kill Ability: [2K+: ${this.getMultiKillCount(analysis.team1)}] vs [2K+: ${this.getMultiKillCount(analysis.team2)}]`);
        // Player impact ratings
        if (analysis.playerImpactRatings.team1.length > 0 || analysis.playerImpactRatings.team2.length > 0) {
            lines.push(`├─ 🧮 Player Impact Ratings:`);
            lines.push(`│   ├─ ${analysis.team1} Top 3: ${this.formatPlayerRatings(analysis.playerImpactRatings.team1)}`);
            lines.push(`│   └─ ${analysis.team2} Top 3: ${this.formatPlayerRatings(analysis.playerImpactRatings.team2)}`);
        }
        // Form trend
        const team1Trend = this.getTrendEmoji(analysis.teamDominanceScores.team1.metrics.winRate);
        const team2Trend = this.getTrendEmoji(analysis.teamDominanceScores.team2.metrics.winRate);
        lines.push(`└─ 📈 Form Trend: [${analysis.team1}: ${team1Trend}] | [${analysis.team2}: ${team2Trend}]`);
        lines.push('');
        // Head-to-head analysis
        lines.push(`🔍 HEAD-TO-HEAD (Last 12 Months):`);
        if (analysis.headToHead.seriesRecord.totalSeries > 0) {
            lines.push(`├─ Series Record: ${analysis.headToHead.seriesRecord.team1Wins}-${analysis.headToHead.seriesRecord.team2Wins} in favor of ${this.getH2HLeader(analysis)}`);
            lines.push(`├─ Map Record: ${analysis.headToHead.mapRecord.team1Wins}-${analysis.headToHead.mapRecord.team2Wins} total maps played`);
            lines.push(`├─ Average Score Differential: ${analysis.headToHead.averageScoreDifferential} rounds per map`);
            lines.push(`├─ Recent Encounters: ${this.formatRecentEncounters(analysis.headToHead.recentEncounters)}`);
        }
        else {
            lines.push(`├─ No recent head-to-head data available`);
        }
        lines.push(`└─ Key Matchup: ${this.getKeyMatchup(analysis)}`);
        lines.push('');
        // Data validation status
        lines.push(`⚠️ DATA VALIDATION STATUS:`);
        lines.push(`├─ ✅ Real Data Only: ${analysis.dataValidation.realDataOnly ? 'No simulated/mock data detected' : 'WARNING: Simulated data detected'}`);
        lines.push(`├─ 📊 Data Completeness: ${analysis.dataValidation.dataCompleteness}% of required metrics available`);
        lines.push(`├─ 🕐 Data Freshness: ${analysis.dataValidation.dataFreshness}`);
        lines.push(`├─ 🎮 Sample Size: [${analysis.team1}: ${analysis.dataValidation.sampleSize.team1} games] | [${analysis.team2}: ${analysis.dataValidation.sampleSize.team2} games]`);
        lines.push(`└─ 🔍 API Health: [Response time: ${analysis.dataValidation.apiHealth.responseTime}ms] | [Rate limit: ${analysis.dataValidation.apiHealth.rateLimit}]`);
        lines.push('');
        // Time-sensitive factors
        lines.push(`📅 TIME-SENSITIVE FACTORS:`);
        lines.push(`├─ Days Since Last Match: [${analysis.team1}: ${analysis.timeSensitiveFactors.daysSinceLastMatch.team1} days] | [${analysis.team2}: ${analysis.timeSensitiveFactors.daysSinceLastMatch.team2} days]`);
        lines.push(`├─ Tournament Context: ${analysis.timeSensitiveFactors.tournamentContext}`);
        lines.push(`├─ Schedule Intensity: ${analysis.timeSensitiveFactors.scheduleIntensity} games in last 7 days`);
        lines.push(`└─ Peak Performance Window: ${analysis.timeSensitiveFactors.peakPerformanceWindow}`);
        lines.push('');
        // Monitoring schedule
        lines.push(`🔄 MONITORING SCHEDULE:`);
        lines.push(`├─ Next Data Update: Every 30 minutes`);
        lines.push(`├─ Pre-Match Update: 2 hours before match`);
        lines.push(`├─ Key Metrics Watch: Roster changes, odds movement`);
        lines.push(`└─ Live Adjustment Triggers: Significant odds shifts >10%`);
        return lines.join('\n');
    }
    /**
     * Format top matches summary
     */
    static formatTopMatchesSummary(analyses, dateSelection) {
        const lines = [];
        lines.push(`📅 ${dateSelection} CS2 BETTING OPPORTUNITIES`);
        lines.push('');
        // Table header
        lines.push('RANK │ MATCH TIME (UTC)     │ TEAMS              │ BET TYPE    │ ODDS │ EV   │ CONFIDENCE │');
        lines.push('─────┼─────────────────────┼────────────────────┼─────────────┼──────┼──────┼────────────┤');
        // Top 5 matches
        const topMatches = analyses.slice(0, 5);
        let totalEV = 0;
        let totalKelly = 0;
        topMatches.forEach((analysis, index) => {
            const rank = (index + 1).toString().padStart(2);
            const matchTime = dateUtils_1.DateManager.formatDisplayDate(analysis.scheduledTime).split(' ')[1].substring(0, 5);
            const teams = `${analysis.team1} vs ${analysis.team2}`.substring(0, 18).padEnd(18);
            const betType = analysis.recommendation.bet.substring(0, 11).padEnd(11);
            const odds = analysis.recommendation.odds.toFixed(2);
            const ev = `${analysis.recommendation.expectedValue > 0 ? '+' : ''}${analysis.recommendation.expectedValue.toFixed(1)}%`;
            const confidence = `${analysis.recommendation.confidence}% ${this.getRiskEmoji(analysis.recommendation.risk)}`;
            lines.push(`  ${rank}  │ ${matchTime}               │ ${teams} │ ${betType} │ ${odds} │ ${ev.padStart(5)} │ ${confidence.padEnd(10)} │`);
            totalEV += analysis.recommendation.expectedValue;
            totalKelly += analysis.recommendation.kellyStake;
        });
        lines.push('');
        lines.push(`📊 Total Expected Value: ${totalEV > 0 ? '+' : ''}${totalEV.toFixed(1)}% | Combined Kelly: ${totalKelly.toFixed(1)} units`);
        lines.push(`⚠️ All predictions based on verified real-time GRID API data only`);
        return lines.join('\n');
    }
    /**
     * Get risk emoji for tier
     */
    static getRiskEmoji(risk) {
        const emojis = { LOW: '🛡️', MEDIUM: '⚖️', HIGH: '⚠️', EXCLUDE: '❌' };
        return emojis[risk];
    }
    /**
     * Get trend emoji based on win rate
     */
    static getTrendEmoji(winRate) {
        if (winRate >= 0.7)
            return '📈';
        if (winRate <= 0.4)
            return '📉';
        return '➡️';
    }
    /**
     * Format player ratings
     */
    static formatPlayerRatings(players) {
        if (players.length === 0)
            return 'No data available';
        return players
            .slice(0, 3)
            .map(p => `[${p.name}: ${p.rating.toFixed(1)}]`)
            .join(' ');
    }
    /**
     * Get multi-kill count (simplified)
     */
    static getMultiKillCount(teamName) {
        return 'N/A'; // Would be calculated from actual data
    }
    /**
     * Get head-to-head leader
     */
    static getH2HLeader(analysis) {
        const h2h = analysis.headToHead.seriesRecord;
        if (h2h.team1Wins > h2h.team2Wins)
            return analysis.team1;
        if (h2h.team2Wins > h2h.team1Wins)
            return analysis.team2;
        return 'Tied';
    }
    /**
     * Format recent encounters
     */
    static formatRecentEncounters(encounters) {
        if (encounters.length === 0)
            return 'No recent encounters';
        return encounters
            .slice(0, 3)
            .map(e => `${e.winner} (${e.score})`)
            .join(', ');
    }
    /**
     * Get key matchup description
     */
    static getKeyMatchup(analysis) {
        const team1Top = analysis.playerImpactRatings.team1[0];
        const team2Top = analysis.playerImpactRatings.team2[0];
        if (team1Top && team2Top) {
            return `${team1Top.name} vs ${team2Top.name} (${team1Top.rating.toFixed(1)} vs ${team2Top.rating.toFixed(1)} PIR)`;
        }
        return 'Star player matchup data unavailable';
    }
}
exports.PredictionFormatter = PredictionFormatter;
//# sourceMappingURL=predictionFormatter.js.map