"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessment = void 0;
class RiskAssessment {
    static assessRiskTier(team1TDS, team2TDS, recentForm, dataQuality) {
        if (this.shouldExclude(team1TDS, team2TDS, dataQuality)) {
            return 'EXCLUDE';
        }
        const tdsGap = Math.abs(team1TDS.score - team2TDS.score);
        const strongerTeamForm = team1TDS.score > team2TDS.score ?
            recentForm.team1WinRate : recentForm.team2WinRate;
        const minSampleSize = Math.min(dataQuality.sampleSize.team1, dataQuality.sampleSize.team2);
        const minConfidence = this.getMinConfidenceScore(team1TDS.confidence, team2TDS.confidence);
        for (const tierDef of this.RISK_TIERS) {
            if (this.meetsCriteria(tdsGap, strongerTeamForm, dataQuality.realDataPercent, minSampleSize, minConfidence, tierDef.criteria)) {
                return tierDef.tier;
            }
        }
        return 'EXCLUDE';
    }
    static shouldExclude(team1TDS, team2TDS, dataQuality) {
        if (dataQuality.sampleSize.team1 < 5 || dataQuality.sampleSize.team2 < 5) {
            return true;
        }
        if (dataQuality.realDataPercent < 75) {
            return true;
        }
        if (!team1TDS.score || !team2TDS.score ||
            team1TDS.score === 0 || team2TDS.score === 0) {
            return true;
        }
        if (team1TDS.dataPoints < 5 || team2TDS.dataPoints < 5) {
            return true;
        }
        return false;
    }
    static meetsCriteria(tdsGap, recentFormWinRate, dataQualityPercent, gamesSample, confidenceScore, criteria) {
        return (tdsGap >= criteria.tdsGap &&
            recentFormWinRate >= criteria.recentFormWinRate &&
            dataQualityPercent >= criteria.dataQualityPercent &&
            gamesSample >= criteria.gamesSample &&
            confidenceScore >= criteria.confidenceThreshold);
    }
    static getMinConfidenceScore(conf1, conf2) {
        const scores = { HIGH: 90, MEDIUM: 75, LOW: 60 };
        return Math.min(scores[conf1], scores[conf2]);
    }
    static generateBettingRecommendation(team1Name, team2Name, team1TDS, team2TDS, odds, riskTier) {
        if (riskTier === 'EXCLUDE') {
            return null;
        }
        const isTeam1Stronger = team1TDS.score > team2TDS.score;
        const strongerTeam = isTeam1Stronger ? team1Name : team2Name;
        const strongerTDS = isTeam1Stronger ? team1TDS : team2TDS;
        const recommendedOdds = isTeam1Stronger ? odds.team1 : odds.team2;
        const tdsGap = Math.abs(team1TDS.score - team2TDS.score);
        const baseProbability = 0.5 + (tdsGap / 200);
        const adjustedProbability = Math.min(baseProbability, 0.85);
        const confidenceScore = this.calculateOverallConfidence(team1TDS, team2TDS, riskTier);
        const expectedValue = this.calculateExpectedValue(adjustedProbability, recommendedOdds, confidenceScore);
        const kellyStake = this.calculateKellyStake(adjustedProbability, recommendedOdds, confidenceScore);
        const maxRisk = this.getMaxRiskForTier(riskTier);
        return {
            bet: `${strongerTeam} ML`,
            odds: recommendedOdds,
            risk: riskTier,
            confidence: Math.round(confidenceScore),
            expectedValue: Math.round(expectedValue * 10) / 10,
            kellyStake: Math.round(kellyStake * 100) / 100,
            maxRisk: maxRisk
        };
    }
    static calculateOverallConfidence(team1TDS, team2TDS, riskTier) {
        const conf1 = this.confidenceToScore(team1TDS.confidence);
        const conf2 = this.confidenceToScore(team2TDS.confidence);
        const avgConfidence = (conf1 + conf2) / 2;
        const tierMultiplier = { LOW: 1.0, MEDIUM: 0.9, HIGH: 0.8, EXCLUDE: 0 };
        return avgConfidence * tierMultiplier[riskTier];
    }
    static confidenceToScore(confidence) {
        const scores = { HIGH: 90, MEDIUM: 75, LOW: 60 };
        return scores[confidence];
    }
    static calculateExpectedValue(probability, odds, confidence) {
        const adjustedProbability = probability * (confidence / 100);
        return (adjustedProbability * (odds - 1)) - (1 - adjustedProbability);
    }
    static calculateKellyStake(probability, odds, confidence) {
        const adjustedProbability = probability * (confidence / 100);
        const b = odds - 1;
        const p = adjustedProbability;
        const q = 1 - p;
        const kellyFraction = (b * p - q) / b;
        const maxStake = 0.05;
        return Math.max(0, Math.min(kellyFraction, maxStake));
    }
    static getMaxRiskForTier(tier) {
        const maxRisks = { LOW: 2, MEDIUM: 3, HIGH: 5, EXCLUDE: 0 };
        return maxRisks[tier];
    }
    static getRiskTierDefinition(tier) {
        return this.RISK_TIERS.find(t => t.tier === tier) || null;
    }
    static getAllRiskTiers() {
        return [...this.RISK_TIERS];
    }
    static validateRiskParameters(team1TDS, team2TDS, dataQuality) {
        const errors = [];
        if (!team1TDS || !team2TDS) {
            errors.push('Missing team dominance scores');
        }
        if (dataQuality.realDataPercent < 50) {
            errors.push('Data quality too low for reliable assessment');
        }
        if (dataQuality.sampleSize.team1 < 3 || dataQuality.sampleSize.team2 < 3) {
            errors.push('Sample size too small for any assessment');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.RiskAssessment = RiskAssessment;
RiskAssessment.RISK_TIERS = [
    {
        tier: 'LOW',
        criteria: {
            tdsGap: 20,
            recentFormWinRate: 0.80,
            dataQualityPercent: 95,
            gamesSample: 15,
            confidenceThreshold: 85
        },
        description: 'High confidence, low risk betting opportunity',
        emoji: '🛡️'
    },
    {
        tier: 'MEDIUM',
        criteria: {
            tdsGap: 12,
            recentFormWinRate: 0.65,
            dataQualityPercent: 85,
            gamesSample: 10,
            confidenceThreshold: 75
        },
        description: 'Moderate confidence, balanced risk-reward',
        emoji: '⚖️'
    },
    {
        tier: 'HIGH',
        criteria: {
            tdsGap: 5,
            recentFormWinRate: 0.55,
            dataQualityPercent: 75,
            gamesSample: 7,
            confidenceThreshold: 65
        },
        description: 'Lower confidence, higher risk betting',
        emoji: '⚠️'
    }
];
//# sourceMappingURL=riskAssessment.js.map