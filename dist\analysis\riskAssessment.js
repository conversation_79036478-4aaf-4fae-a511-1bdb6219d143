"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessment = void 0;
class RiskAssessment {
    /**
     * Assess risk tier for a betting opportunity
     */
    static assessRiskTier(team1TDS, team2TDS, recentForm, dataQuality) {
        // Check for automatic exclusions first
        if (this.shouldExclude(team1TDS, team2TDS, dataQuality)) {
            return 'EXCLUDE';
        }
        // Calculate TDS gap (absolute difference)
        const tdsGap = Math.abs(team1TDS.score - team2TDS.score);
        // Get the stronger team's recent form
        const strongerTeamForm = team1TDS.score > team2TDS.score ?
            recentForm.team1WinRate : recentForm.team2WinRate;
        // Get minimum sample size
        const minSampleSize = Math.min(dataQuality.sampleSize.team1, dataQuality.sampleSize.team2);
        // Get minimum confidence
        const minConfidence = this.getMinConfidenceScore(team1TDS.confidence, team2TDS.confidence);
        // Evaluate against risk tiers (from lowest to highest risk)
        for (const tierDef of this.RISK_TIERS) {
            if (this.meetsCriteria(tdsGap, strongerTeamForm, dataQuality.realDataPercent, minSampleSize, minConfidence, tierDef.criteria)) {
                return tierDef.tier;
            }
        }
        // If no tier criteria met, exclude
        return 'EXCLUDE';
    }
    /**
     * Check if betting opportunity should be automatically excluded
     */
    static shouldExclude(team1TDS, team2TDS, dataQuality) {
        // Insufficient game sample
        if (dataQuality.sampleSize.team1 < 5 || dataQuality.sampleSize.team2 < 5) {
            return true;
        }
        // Poor data quality
        if (dataQuality.realDataPercent < 75) {
            return true;
        }
        // Missing core statistics
        if (!team1TDS.score || !team2TDS.score ||
            team1TDS.score === 0 || team2TDS.score === 0) {
            return true;
        }
        // Data points too low
        if (team1TDS.dataPoints < 5 || team2TDS.dataPoints < 5) {
            return true;
        }
        return false;
    }
    /**
     * Check if criteria are met for a specific risk tier
     */
    static meetsCriteria(tdsGap, recentFormWinRate, dataQualityPercent, gamesSample, confidenceScore, criteria) {
        return (tdsGap >= criteria.tdsGap &&
            recentFormWinRate >= criteria.recentFormWinRate &&
            dataQualityPercent >= criteria.dataQualityPercent &&
            gamesSample >= criteria.gamesSample &&
            confidenceScore >= criteria.confidenceThreshold);
    }
    /**
     * Convert confidence level to numeric score
     */
    static getMinConfidenceScore(conf1, conf2) {
        const scores = { HIGH: 90, MEDIUM: 75, LOW: 60 };
        return Math.min(scores[conf1], scores[conf2]);
    }
    /**
     * Generate betting recommendation based on risk assessment
     */
    static generateBettingRecommendation(team1Name, team2Name, team1TDS, team2TDS, odds, riskTier) {
        if (riskTier === 'EXCLUDE') {
            return null;
        }
        // Determine stronger team
        const isTeam1Stronger = team1TDS.score > team2TDS.score;
        const strongerTeam = isTeam1Stronger ? team1Name : team2Name;
        const strongerTDS = isTeam1Stronger ? team1TDS : team2TDS;
        const recommendedOdds = isTeam1Stronger ? odds.team1 : odds.team2;
        // Calculate win probability based on TDS difference
        const tdsGap = Math.abs(team1TDS.score - team2TDS.score);
        const baseProbability = 0.5 + (tdsGap / 200); // Convert TDS gap to probability
        const adjustedProbability = Math.min(baseProbability, 0.85); // Cap at 85%
        // Calculate confidence score
        const confidenceScore = this.calculateOverallConfidence(team1TDS, team2TDS, riskTier);
        // Calculate expected value
        const expectedValue = this.calculateExpectedValue(adjustedProbability, recommendedOdds, confidenceScore);
        // Calculate Kelly stake
        const kellyStake = this.calculateKellyStake(adjustedProbability, recommendedOdds, confidenceScore);
        // Determine max risk based on tier
        const maxRisk = this.getMaxRiskForTier(riskTier);
        return {
            bet: `${strongerTeam} ML`,
            odds: recommendedOdds,
            risk: riskTier,
            confidence: Math.round(confidenceScore),
            expectedValue: Math.round(expectedValue * 10) / 10,
            kellyStake: Math.round(kellyStake * 100) / 100,
            maxRisk: maxRisk
        };
    }
    /**
     * Calculate overall confidence score
     */
    static calculateOverallConfidence(team1TDS, team2TDS, riskTier) {
        const conf1 = this.confidenceToScore(team1TDS.confidence);
        const conf2 = this.confidenceToScore(team2TDS.confidence);
        const avgConfidence = (conf1 + conf2) / 2;
        // Adjust based on risk tier
        const tierMultiplier = { LOW: 1.0, MEDIUM: 0.9, HIGH: 0.8, EXCLUDE: 0 };
        return avgConfidence * tierMultiplier[riskTier];
    }
    /**
     * Convert confidence level to numeric score
     */
    static confidenceToScore(confidence) {
        const scores = { HIGH: 90, MEDIUM: 75, LOW: 60 };
        return scores[confidence];
    }
    /**
     * Calculate expected value
     */
    static calculateExpectedValue(probability, odds, confidence) {
        const adjustedProbability = probability * (confidence / 100);
        return (adjustedProbability * (odds - 1)) - (1 - adjustedProbability);
    }
    /**
     * Calculate Kelly criterion stake
     */
    static calculateKellyStake(probability, odds, confidence) {
        const adjustedProbability = probability * (confidence / 100);
        const b = odds - 1;
        const p = adjustedProbability;
        const q = 1 - p;
        const kellyFraction = (b * p - q) / b;
        // Apply maximum stake limits based on risk
        const maxStake = 0.05; // 5% maximum
        return Math.max(0, Math.min(kellyFraction, maxStake));
    }
    /**
     * Get maximum risk percentage for tier
     */
    static getMaxRiskForTier(tier) {
        const maxRisks = { LOW: 2, MEDIUM: 3, HIGH: 5, EXCLUDE: 0 };
        return maxRisks[tier];
    }
    /**
     * Get risk tier definition
     */
    static getRiskTierDefinition(tier) {
        return this.RISK_TIERS.find(t => t.tier === tier) || null;
    }
    /**
     * Get all risk tier definitions
     */
    static getAllRiskTiers() {
        return [...this.RISK_TIERS];
    }
    /**
     * Validate risk assessment parameters
     */
    static validateRiskParameters(team1TDS, team2TDS, dataQuality) {
        const errors = [];
        if (!team1TDS || !team2TDS) {
            errors.push('Missing team dominance scores');
        }
        if (dataQuality.realDataPercent < 50) {
            errors.push('Data quality too low for reliable assessment');
        }
        if (dataQuality.sampleSize.team1 < 3 || dataQuality.sampleSize.team2 < 3) {
            errors.push('Sample size too small for any assessment');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.RiskAssessment = RiskAssessment;
// Risk tier definitions from the prompt specifications
RiskAssessment.RISK_TIERS = [
    {
        tier: 'LOW',
        criteria: {
            tdsGap: 20,
            recentFormWinRate: 0.80,
            dataQualityPercent: 95,
            gamesSample: 15,
            confidenceThreshold: 85
        },
        description: 'High confidence, low risk betting opportunity',
        emoji: '🛡️'
    },
    {
        tier: 'MEDIUM',
        criteria: {
            tdsGap: 12,
            recentFormWinRate: 0.65,
            dataQualityPercent: 85,
            gamesSample: 10,
            confidenceThreshold: 75
        },
        description: 'Moderate confidence, balanced risk-reward',
        emoji: '⚖️'
    },
    {
        tier: 'HIGH',
        criteria: {
            tdsGap: 5,
            recentFormWinRate: 0.55,
            dataQualityPercent: 75,
            gamesSample: 7,
            confidenceThreshold: 65
        },
        description: 'Lower confidence, higher risk betting',
        emoji: '⚠️'
    }
];
//# sourceMappingURL=riskAssessment.js.map