import { MatchAnalysis, DateSelection } from '../types';
export declare class PredictionFormatter {
    /**
     * Format complete match analysis according to prompt specifications
     */
    static formatMatchAnalysis(analysis: MatchAnalysis, dateSelection: DateSelection): string;
    /**
     * Format top matches summary
     */
    static formatTopMatchesSummary(analyses: MatchAnalysis[], dateSelection: DateSelection): string;
    /**
     * Get risk emoji for tier
     */
    private static getRiskEmoji;
    /**
     * Get trend emoji based on win rate
     */
    private static getTrendEmoji;
    /**
     * Format player ratings
     */
    private static formatPlayerRatings;
    /**
     * Get multi-kill count (simplified)
     */
    private static getMultiKillCount;
    /**
     * Get head-to-head leader
     */
    private static getH2HLeader;
    /**
     * Format recent encounters
     */
    private static formatRecentEncounters;
    /**
     * Get key matchup description
     */
    private static getKeyMatchup;
}
//# sourceMappingURL=predictionFormatter.d.ts.map