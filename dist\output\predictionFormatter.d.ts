import { MatchAnalysis, DateSelection } from '../types';
export declare class PredictionFormatter {
    static formatMatchAnalysis(analysis: MatchAnalysis, dateSelection: DateSelection): string;
    static formatTopMatchesSummary(analyses: MatchAnalysis[], dateSelection: DateSelection): string;
    private static getRiskEmoji;
    private static getTrendEmoji;
    private static formatPlayerRatings;
    private static getMultiKillCount;
    private static getH2HLeader;
    private static formatRecentEncounters;
    private static getKeyMatchup;
}
//# sourceMappingURL=predictionFormatter.d.ts.map