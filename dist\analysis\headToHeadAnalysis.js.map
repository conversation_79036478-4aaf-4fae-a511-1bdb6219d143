{"version": 3, "file": "headToHeadAnalysis.js", "sourceRoot": "", "sources": ["../../src/analysis/headToHeadAnalysis.ts"], "names": [], "mappings": ";;;AACA,kDAAiD;AAEjD,MAAa,kBAAkB;IAK7B,MAAM,CAAC,iBAAiB,CACtB,OAAe,EACf,OAAe,EACf,SAAiB,EACjB,SAAiB,EACjB,UAAiC;QAGjC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACjC,CAAC;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAGvD,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAGxE,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAGlE,MAAM,wBAAwB,GAAG,IAAI,CAAC,iCAAiC,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAGlG,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAElG,OAAO;YACL,YAAY;YACZ,SAAS;YACT,wBAAwB;YACxB,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,mBAAmB,CAChC,OAAe,EACf,OAAe,EACf,MAAmB;QAGnB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAClD,SAAS;YACX,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBAC/B,SAAS,EAAE,CAAC;YACd,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACtC,SAAS,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO;YACL,SAAS;YACT,SAAS;YACT,WAAW,EAAE,SAAS,GAAG,SAAS;SACnC,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,gBAAgB,CAC7B,OAAe,EACf,OAAe,EACf,MAAmB;QAGnB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,SAAS;YACX,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChF,SAAS;gBACX,CAAC;gBAGD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC;gBAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC;gBAE9E,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBACzD,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;wBAC5B,SAAS,EAAE,CAAC;oBACd,CAAC;yBAAM,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;wBACnC,SAAS,EAAE,CAAC;oBACd,CAAC;gBAEH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,SAAS;YACT,SAAS;YACT,SAAS,EAAE,SAAS,GAAG,SAAS;SACjC,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,iCAAiC,CAC9C,OAAe,EACf,OAAe,EACf,MAAmB;QAGnB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,SAAS;YACX,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChF,SAAS;gBACX,CAAC;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC;gBAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC;gBAE9E,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBACzD,iBAAiB,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC;oBACvD,QAAQ,EAAE,CAAC;gBACb,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,iBAAiB,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;IAKO,MAAM,CAAC,mBAAmB,CAChC,OAAe,EACf,OAAe,EACf,SAAiB,EACjB,SAAiB,EACjB,MAAmB;QAInB,MAAM,YAAY,GAAG,MAAM;aACxB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,CAAC,QAAQ,CAAC;aACjD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;aACrF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;YACtE,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAE3D,OAAO;gBACL,IAAI,EAAE,uBAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC;gBACtD,MAAM,EAAE,UAAU;gBAClB,KAAK;aACN,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,OAAe,EAAE,KAAgB;QAC9E,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QAC3E,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QAE3E,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;QAE5C,OAAO,GAAG,UAAU,IAAI,UAAU,EAAE,CAAC;IACvC,CAAC;IAKO,MAAM,CAAC,gBAAgB;QAC7B,OAAO;YACL,YAAY,EAAE;gBACZ,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,CAAC;aACf;YACD,SAAS,EAAE;gBACT,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,CAAC;aACb;YACD,wBAAwB,EAAE,CAAC;YAC3B,gBAAgB,EAAE,EAAE;SACrB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,qBAAqB,CAAC,QAA4B;QAMvD,IAAI,QAAQ,CAAC,YAAY,CAAC,WAAW,KAAK,CAAC,EAAE,CAAC;YAC5C,OAAO;gBACL,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,KAAK;aACrB,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC;QACzF,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC;QAGzF,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC,IAAI,GAAG,CAAC;QAElE,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;YAC9C,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;YAC9C,aAAa;SACd,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,iBAAiB,CACtB,OAAe,EACf,OAAe,EACf,KAAa,EACb,MAAmB;QAGnB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,SAAS;YACX,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU;oBACzB,CAAC,IAAI,CAAC,GAAG;oBACT,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,KAAK;oBACrB,CAAC,IAAI,CAAC,SAAS;oBACf,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,SAAS;gBACX,CAAC;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC;gBAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC;gBAE9E,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBACzD,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;wBAC5B,SAAS,EAAE,CAAC;oBACd,CAAC;yBAAM,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;wBACnC,SAAS,EAAE,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,SAAS;YACT,SAAS;YACT,SAAS,EAAE,SAAS,GAAG,SAAS;SACjC,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,sBAAsB,CAC3B,OAAe,EACf,OAAe,EACf,MAAmB;QAInB,MAAM,YAAY,GAAG,MAAM;aACxB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,CAAC,QAAQ,CAAC;aACjD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;aACrF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;QACxD,CAAC;QAGD,MAAM,UAAU,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QAC1C,MAAM,SAAS,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAGrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO;gBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;iBACxD,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO;gBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;QACpE,CAAC;QAGD,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO;gBAAE,SAAS,CAAC,KAAK,EAAE,CAAC;iBACvD,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO;gBAAE,SAAS,CAAC,KAAK,EAAE,CAAC;QACnE,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QAC1E,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QAE1E,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;IACpC,CAAC;IAKO,MAAM,CAAC,cAAc,CAAC,UAAkB,EAAE,SAAiB;QACjE,IAAI,UAAU,GAAG,SAAS;YAAE,OAAO,IAAI,CAAC;QACxC,IAAI,UAAU,GAAG,SAAS;YAAE,OAAO,MAAM,CAAC;QAC1C,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA5VD,gDA4VC"}