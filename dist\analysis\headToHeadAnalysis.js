"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HeadToHeadAnalyzer = void 0;
const dateUtils_1 = require("../utils/dateUtils");
class HeadToHeadAnalyzer {
    /**
     * Analyze head-to-head record between two teams
     */
    static analyzeHeadToHead(team1Id, team2Id, team1Name, team2Name, seriesData) {
        if (!seriesData.edges || seriesData.edges.length === 0) {
            return this.getEmptyAnalysis();
        }
        const series = seriesData.edges.map(edge => edge.node);
        // Analyze series record
        const seriesRecord = this.analyzeSeriesRecord(team1Id, team2Id, series);
        // Analyze map record
        const mapRecord = this.analyzeMapRecord(team1Id, team2Id, series);
        // Calculate average score differential
        const averageScoreDifferential = this.calculateAverageScoreDifferential(team1Id, team2Id, series);
        // Get recent encounters
        const recentEncounters = this.getRecentEncounters(team1Id, team2Id, team1Name, team2Name, series);
        return {
            seriesRecord,
            mapRecord,
            averageScoreDifferential,
            recentEncounters
        };
    }
    /**
     * Analyze series win/loss record
     */
    static analyzeSeriesRecord(team1Id, team2Id, series) {
        let team1Wins = 0;
        let team2Wins = 0;
        for (const serie of series) {
            if (serie.state !== 'FINISHED' || !serie.winnerId) {
                continue; // Skip unfinished or undecided series
            }
            if (serie.winnerId === team1Id) {
                team1Wins++;
            }
            else if (serie.winnerId === team2Id) {
                team2Wins++;
            }
        }
        return {
            team1Wins,
            team2Wins,
            totalSeries: team1Wins + team2Wins
        };
    }
    /**
     * Analyze individual map win/loss record
     */
    static analyzeMapRecord(team1Id, team2Id, series) {
        let team1Wins = 0;
        let team2Wins = 0;
        for (const serie of series) {
            if (!serie.games || serie.games.length === 0) {
                continue;
            }
            for (const game of serie.games) {
                if (game.state !== 'FINISHED' || !game.opponents || game.opponents.length !== 2) {
                    continue;
                }
                // Find team scores
                const team1Score = game.opponents.find(opp => opp.team.id === team1Id)?.score;
                const team2Score = game.opponents.find(opp => opp.team.id === team2Id)?.score;
                if (team1Score !== undefined && team2Score !== undefined) {
                    if (team1Score > team2Score) {
                        team1Wins++;
                    }
                    else if (team2Score > team1Score) {
                        team2Wins++;
                    }
                    // Ignore ties
                }
            }
        }
        return {
            team1Wins,
            team2Wins,
            totalMaps: team1Wins + team2Wins
        };
    }
    /**
     * Calculate average score differential per map
     */
    static calculateAverageScoreDifferential(team1Id, team2Id, series) {
        let totalDifferential = 0;
        let mapCount = 0;
        for (const serie of series) {
            if (!serie.games || serie.games.length === 0) {
                continue;
            }
            for (const game of serie.games) {
                if (game.state !== 'FINISHED' || !game.opponents || game.opponents.length !== 2) {
                    continue;
                }
                const team1Score = game.opponents.find(opp => opp.team.id === team1Id)?.score;
                const team2Score = game.opponents.find(opp => opp.team.id === team2Id)?.score;
                if (team1Score !== undefined && team2Score !== undefined) {
                    totalDifferential += Math.abs(team1Score - team2Score);
                    mapCount++;
                }
            }
        }
        return mapCount > 0 ? Math.round((totalDifferential / mapCount) * 10) / 10 : 0;
    }
    /**
     * Get recent encounters with details
     */
    static getRecentEncounters(team1Id, team2Id, team1Name, team2Name, series) {
        // Sort series by date (most recent first)
        const sortedSeries = series
            .filter(s => s.state === 'FINISHED' && s.winnerId)
            .sort((a, b) => new Date(b.scheduledAt).getTime() - new Date(a.scheduledAt).getTime())
            .slice(0, 3); // Get last 3 encounters
        return sortedSeries.map(serie => {
            const winnerName = serie.winnerId === team1Id ? team1Name : team2Name;
            const score = this.getSeriesScore(team1Id, team2Id, serie);
            return {
                date: dateUtils_1.DateManager.formatDisplayDate(serie.scheduledAt),
                winner: winnerName,
                score
            };
        });
    }
    /**
     * Get series score string
     */
    static getSeriesScore(team1Id, team2Id, serie) {
        if (!serie.opponents || serie.opponents.length !== 2) {
            return 'N/A';
        }
        const team1Opponent = serie.opponents.find(opp => opp.team.id === team1Id);
        const team2Opponent = serie.opponents.find(opp => opp.team.id === team2Id);
        if (!team1Opponent || !team2Opponent) {
            return 'N/A';
        }
        const team1Score = team1Opponent.score || 0;
        const team2Score = team2Opponent.score || 0;
        return `${team1Score}-${team2Score}`;
    }
    /**
     * Get empty analysis for when no data is available
     */
    static getEmptyAnalysis() {
        return {
            seriesRecord: {
                team1Wins: 0,
                team2Wins: 0,
                totalSeries: 0
            },
            mapRecord: {
                team1Wins: 0,
                team2Wins: 0,
                totalMaps: 0
            },
            averageScoreDifferential: 0,
            recentEncounters: []
        };
    }
    /**
     * Calculate head-to-head advantage
     */
    static calculateH2HAdvantage(analysis) {
        if (analysis.seriesRecord.totalSeries === 0) {
            return {
                team1Advantage: 0,
                team2Advantage: 0,
                isSignificant: false
            };
        }
        const team1WinRate = analysis.seriesRecord.team1Wins / analysis.seriesRecord.totalSeries;
        const team2WinRate = analysis.seriesRecord.team2Wins / analysis.seriesRecord.totalSeries;
        // Consider significant if at least 3 series and clear advantage
        const isSignificant = analysis.seriesRecord.totalSeries >= 3 &&
            Math.abs(team1WinRate - team2WinRate) >= 0.3;
        return {
            team1Advantage: Math.round(team1WinRate * 100),
            team2Advantage: Math.round(team2WinRate * 100),
            isSignificant
        };
    }
    /**
     * Get map-specific head-to-head for a particular map
     */
    static getMapSpecificH2H(team1Id, team2Id, mapId, series) {
        let team1Wins = 0;
        let team2Wins = 0;
        for (const serie of series) {
            if (!serie.games || serie.games.length === 0) {
                continue;
            }
            for (const game of serie.games) {
                if (game.state !== 'FINISHED' ||
                    !game.map ||
                    game.map.id !== mapId ||
                    !game.opponents ||
                    game.opponents.length !== 2) {
                    continue;
                }
                const team1Score = game.opponents.find(opp => opp.team.id === team1Id)?.score;
                const team2Score = game.opponents.find(opp => opp.team.id === team2Id)?.score;
                if (team1Score !== undefined && team2Score !== undefined) {
                    if (team1Score > team2Score) {
                        team1Wins++;
                    }
                    else if (team2Score > team1Score) {
                        team2Wins++;
                    }
                }
            }
        }
        return {
            team1Wins,
            team2Wins,
            totalMaps: team1Wins + team2Wins
        };
    }
    /**
     * Analyze recent form trend from head-to-head
     */
    static analyzeRecentFormTrend(team1Id, team2Id, series) {
        // Get last 5 encounters
        const recentSeries = series
            .filter(s => s.state === 'FINISHED' && s.winnerId)
            .sort((a, b) => new Date(b.scheduledAt).getTime() - new Date(a.scheduledAt).getTime())
            .slice(0, 5);
        if (recentSeries.length < 3) {
            return { team1Trend: 'STABLE', team2Trend: 'STABLE' };
        }
        // Analyze win pattern (recent vs older)
        const recentWins = { team1: 0, team2: 0 };
        const olderWins = { team1: 0, team2: 0 };
        const midPoint = Math.floor(recentSeries.length / 2);
        // Recent half
        for (let i = 0; i < midPoint; i++) {
            if (recentSeries[i].winnerId === team1Id)
                recentWins.team1++;
            else if (recentSeries[i].winnerId === team2Id)
                recentWins.team2++;
        }
        // Older half
        for (let i = midPoint; i < recentSeries.length; i++) {
            if (recentSeries[i].winnerId === team1Id)
                olderWins.team1++;
            else if (recentSeries[i].winnerId === team2Id)
                olderWins.team2++;
        }
        const team1Trend = this.determineTrend(recentWins.team1, olderWins.team1);
        const team2Trend = this.determineTrend(recentWins.team2, olderWins.team2);
        return { team1Trend, team2Trend };
    }
    /**
     * Determine trend based on recent vs older performance
     */
    static determineTrend(recentWins, olderWins) {
        if (recentWins > olderWins)
            return 'UP';
        if (recentWins < olderWins)
            return 'DOWN';
        return 'STABLE';
    }
}
exports.HeadToHeadAnalyzer = HeadToHeadAnalyzer;
//# sourceMappingURL=headToHeadAnalysis.js.map