import { CS2PlayerStats, HLTVMatch, HLTVPlayer, HLTVTeam, HLTVPlayerStats } from '../types';
export declare class HLTVClient {
    private client;
    private baseURL;
    private requestCount;
    private lastRequestTime;
    private readonly RATE_LIMIT;
    constructor();
    private enforceRateLimit;
    private makeRequest;
    private parseHTML;
    getUpcomingMatches(): Promise<HLTVMatch[]>;
    searchPlayers(playerName: string): Promise<HLTVPlayer[]>;
    getPlayerProfile(playerId: string): Promise<HLTVPlayer>;
    getPlayerStats(playerId: string): Promise<HLTVPlayerStats>;
    getPlayerCareerStats(playerId: string): Promise<CS2PlayerStats>;
    getTeamInfo(teamName: string): Promise<HLTVTeam | null>;
    private transformPlayerStats;
    getAPIHealth(): {
        responseTime: number;
        rateLimit: string;
        requestCount: number;
    };
    validateConnection(): Promise<boolean>;
}
//# sourceMappingURL=hltvClient.d.ts.map