export interface DateRange {
    start: string;
    end: string;
}
export interface DateOptions {
    TODAY: DateRange;
    TOMORROW: DateRange;
    CUSTOM: DateRange;
}
export type DateSelection = 'TODAY' | 'TOMORROW' | 'CUSTOM';
export type RiskTier = 'LOW' | 'MEDIUM' | 'HIGH' | 'EXCLUDE';
export type ConfidenceLevel = 'HIGH' | 'MEDIUM' | 'LOW';
export interface GridAPIResponse<T> {
    data: T;
    errors?: Array<{
        message: string;
        locations?: Array<{
            line: number;
            column: number;
        }>;
        path?: string[];
    }>;
}
export interface PageInfo {
    hasNextPage: boolean;
    endCursor: string;
}
export interface Edge<T> {
    node: T;
    cursor: string;
}
export interface Connection<T> {
    edges: Edge<T>[];
    pageInfo: PageInfo;
    totalCount?: number;
}
export interface CS2Team {
    id: string;
    name: string;
    image?: string;
    players: CS2Player[];
}
export interface CS2Player {
    id: string;
    name: string;
    image?: string;
    nationality?: string;
    team?: {
        id: string;
        name: string;
    };
}
export interface CS2Tournament {
    id: string;
    name: string;
    tier?: string;
}
export interface CS2Map {
    id: string;
    name: string;
    image?: string;
}
export interface CS2Game {
    id: string;
    position: number;
    state: string;
    map?: CS2Map;
    opponents: Array<{
        score: number;
        team: {
            id: string;
            name: string;
        };
    }>;
}
export interface CS2Series {
    id: string;
    name: string;
    bestOf: number;
    scheduledAt: string;
    state: string;
    winnerId?: string;
    tournament: CS2Tournament;
    opponents: Array<{
        id: string;
        name: string;
        image?: string;
        score?: number;
        team: CS2Team;
    }>;
    games: CS2Game[];
}
export interface MultiKills {
    k2: number;
    k3: number;
    k4: number;
    k5: number;
}
export interface ClutchStats {
    won1v1: number;
    won1v2: number;
    won1v3: number;
    won1v4: number;
    won1v5: number;
    total1v1: number;
    total1v2: number;
    total1v3: number;
    total1v4: number;
    total1v5: number;
}
export interface CS2TeamStats {
    gamesCount: number;
    gamesWonCount: number;
    gamesLostCount: number;
    seriesCount: number;
    seriesWonCount: number;
    seriesLostCount: number;
    roundsCount: number;
    roundsWonCount: number;
    roundsLostCount: number;
    kills: number;
    deaths: number;
    assists: number;
    headshots: number;
    headshotPercentage: number;
    kdr: number;
    adr: number;
    rating: number;
    firstKills: number;
    firstDeaths: number;
    multiKills: MultiKills;
    clutches: ClutchStats;
}
export interface CS2PlayerStats {
    gamesCount: number;
    gamesWonCount: number;
    gamesLostCount: number;
    roundsCount: number;
    roundsWonCount: number;
    roundsLostCount: number;
    kills: number;
    deaths: number;
    assists: number;
    headshots: number;
    headshotPercentage: number;
    kdr: number;
    adr: number;
    rating: number;
    firstKills: number;
    firstDeaths: number;
    clutchesWon: number;
    clutchesTotal: number;
    multiKills: MultiKills;
    maps: Array<{
        mapId: string;
        mapName: string;
        gamesCount: number;
        rating: number;
        kdr: number;
        adr: number;
    }>;
}
export interface TeamDominanceScore {
    score: number;
    confidence: ConfidenceLevel;
    dataPoints: number;
    lastUpdate: string;
    metrics: {
        winRate: number;
        kdr: number;
        adr: number;
        rating: number;
        headshotRate: number;
        clutchSuccess: number;
        firstKillRate: number;
    };
}
export interface PlayerImpactRating {
    playerId: string;
    name: string;
    rating: number;
    gamesPlayed: number;
    confidence: ConfidenceLevel;
    metrics: {
        kdr: number;
        adr: number;
        rating: number;
        headshotRate: number;
        clutchRate: number;
    };
}
export interface HeadToHeadAnalysis {
    seriesRecord: {
        team1Wins: number;
        team2Wins: number;
        totalSeries: number;
    };
    mapRecord: {
        team1Wins: number;
        team2Wins: number;
        totalMaps: number;
    };
    averageScoreDifferential: number;
    recentEncounters: Array<{
        date: string;
        winner: string;
        score: string;
    }>;
}
export interface BettingRecommendation {
    bet: string;
    odds: number;
    risk: RiskTier;
    confidence: number;
    expectedValue: number;
    kellyStake: number;
    maxRisk: number;
}
export interface MatchAnalysis {
    matchId: string;
    team1: string;
    team2: string;
    tournament: string;
    format: string;
    scheduledTime: string;
    currentOdds: {
        team1: number;
        team2: number;
        movement?: string;
    };
    recommendation: BettingRecommendation;
    teamDominanceScores: {
        team1: TeamDominanceScore;
        team2: TeamDominanceScore;
    };
    playerImpactRatings: {
        team1: PlayerImpactRating[];
        team2: PlayerImpactRating[];
    };
    headToHead: HeadToHeadAnalysis;
    dataValidation: {
        realDataOnly: boolean;
        dataCompleteness: number;
        dataFreshness: string;
        sampleSize: {
            team1: number;
            team2: number;
        };
        apiHealth: {
            responseTime: number;
            rateLimit: string;
        };
    };
    timeSensitiveFactors: {
        daysSinceLastMatch: {
            team1: number;
            team2: number;
        };
        tournamentContext: string;
        scheduleIntensity: number;
        peakPerformanceWindow: string;
    };
}
export interface AnalysisConfig {
    dateSelection: DateSelection;
    customDateRange?: DateRange;
    minGamesThreshold: number;
    confidenceThreshold: number;
    maxRiskTier: RiskTier;
    includePlayerAnalysis: boolean;
    includeMapAnalysis: boolean;
}
export interface HLTVPlayer {
    id: string;
    name: string;
    nickname?: string;
    country?: string;
    team?: string;
    age?: number;
    image?: string;
}
export interface HLTVTeam {
    id: string;
    name: string;
    country?: string;
    logo?: string;
    players?: HLTVPlayer[];
    ranking?: number;
}
export interface HLTVEvent {
    id: string;
    name: string;
    startDate?: string;
    endDate?: string;
    location?: string;
    prizePool?: string;
    teams?: HLTVTeam[];
    matches?: HLTVMatch[];
}
export interface HLTVMatch {
    id: string;
    team1: HLTVTeam;
    team2: HLTVTeam;
    event: string;
    date: string;
    format: string;
    status: 'upcoming' | 'live' | 'finished';
    result?: {
        winner: string;
        score: string;
    };
}
export interface HLTVPlayerStats {
    rating: number;
    rating2?: number;
    kills: number;
    deaths: number;
    assists: number;
    kd_ratio: number;
    adr: number;
    headshot_percentage: number;
    maps_played: number;
    rounds_played: number;
    first_kills: number;
    first_deaths: number;
    clutches_won: number;
    clutches_total: number;
    multi_kills?: {
        k2: number;
        k3: number;
        k4: number;
        k5: number;
    };
    win_rate?: number;
}
export interface HLTVTeamProfile {
    id: string;
    name: string;
    country: string;
    logo: string;
    players: HLTVPlayer[];
    ranking: number;
    recent_results?: Array<{
        opponent: string;
        result: 'win' | 'loss';
        score: string;
        date: string;
    }>;
}
//# sourceMappingURL=index.d.ts.map