#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { AnalysisEngine } from './analysis/analysisEngine';
import { PredictionFormatter } from './output/predictionFormatter';
import { DateManager } from './utils/dateUtils';
import { DateSelection, AnalysisConfig, DateRange } from './types';

const program = new Command();

// GRID API Key from prompt
const GRID_API_KEY = 'cDXLTwMg735stCkxIaaAEdlkYlkKcu88dwjshjYn';

// Default configuration
const DEFAULT_CONFIG: AnalysisConfig = {
  dateSelection: 'TODAY',
  minGamesThreshold: 5,
  confidenceThreshold: 65,
  maxRiskTier: 'HIGH',
  includePlayerAnalysis: true,
  includeMapAnalysis: false
};

program
  .name('cs2-betting-analysis')
  .description('CS2 Betting Analysis Framework with GRID API Integration')
  .version('1.0.0');

program
  .command('analyze')
  .description('Analyze CS2 matches for betting opportunities')
  .option('-d, --date <type>', 'Date selection: TODAY, TOMORROW, or CUSTOM', 'TODAY')
  .option('--start <date>', 'Custom start date (YYYY-MM-DD)')
  .option('--end <date>', 'Custom end date (YYYY-MM-DD)')
  .option('--min-games <number>', 'Minimum games threshold', '5')
  .option('--confidence <number>', 'Minimum confidence threshold', '65')
  .option('--max-risk <tier>', 'Maximum risk tier: LOW, MEDIUM, HIGH', 'HIGH')
  .option('--no-players', 'Disable player analysis')
  .option('--include-maps', 'Include map-specific analysis')
  .option('--summary-only', 'Show only top matches summary')
  .action(async (options) => {
    try {
      console.log(chalk.blue.bold('🎯 CS2 Betting Analysis Framework'));
      console.log(chalk.blue('📊 Enhanced Prediction with GRID API Integration'));
      console.log(chalk.yellow('⚠️  CRITICAL: Using only real, live data - NO MOCK DATA'));
      console.log('');

      // Validate and parse options
      const config = await parseOptions(options);

      // Create analysis engine
      const engine = new AnalysisEngine(GRID_API_KEY, config);

      // Validate configuration
      const validation = engine.validateConfig();
      if (!validation.isValid) {
        console.error(chalk.red('❌ Configuration errors:'));
        validation.errors.forEach(error => console.error(chalk.red(`   - ${error}`)));
        process.exit(1);
      }

      // Determine date selection and custom dates
      let dateSelection: DateSelection = config.dateSelection;
      let customDates: DateRange | undefined;

      if (dateSelection === 'CUSTOM') {
        if (!options.start || !options.end) {
          console.error(chalk.red('❌ Custom date range requires --start and --end parameters'));
          process.exit(1);
        }

        customDates = {
          start: DateManager.parseCustomDate(options.start),
          end: DateManager.parseCustomDate(options.end)
        };

        if (!DateManager.validateDateRange(customDates)) {
          console.error(chalk.red('❌ Invalid date range'));
          process.exit(1);
        }
      }

      // Run analysis
      console.log(chalk.green('🚀 Starting analysis...'));
      const startTime = Date.now();

      const analyses = await engine.analyzeMatches(dateSelection, customDates);

      const duration = Date.now() - startTime;
      console.log(chalk.green(`✅ Analysis completed in ${duration}ms`));
      console.log('');

      // Display results
      if (analyses.length === 0) {
        console.log(chalk.yellow('⚠️ No betting opportunities found for the specified criteria'));
        return;
      }

      if (options.summaryOnly) {
        // Show summary only
        const summary = PredictionFormatter.formatTopMatchesSummary(analyses, dateSelection);
        console.log(summary);
      } else {
        // Show detailed analysis for each match
        analyses.forEach((analysis, index) => {
          if (index > 0) console.log('\n' + '='.repeat(80) + '\n');

          const formatted = PredictionFormatter.formatMatchAnalysis(analysis, dateSelection);
          console.log(formatted);
        });

        // Show summary at the end
        console.log('\n' + '='.repeat(80));
        console.log(chalk.blue.bold('📊 SUMMARY'));
        console.log('='.repeat(80) + '\n');

        const summary = PredictionFormatter.formatTopMatchesSummary(analyses, dateSelection);
        console.log(summary);
      }

    } catch (error) {
      console.error(chalk.red('❌ Analysis failed:'), error);
      process.exit(1);
    }
  });

program
  .command('test-api')
  .description('Test GRID API connection')
  .action(async () => {
    try {
      console.log(chalk.blue('🔍 Testing GRID API connection...'));

      const engine = new AnalysisEngine(GRID_API_KEY, DEFAULT_CONFIG);
      const isConnected = await engine['gridClient'].validateConnection();

      if (isConnected) {
        console.log(chalk.green('✅ GRID API connection successful'));

        // Test rate limiting
        const health = engine['gridClient'].getAPIHealth();
        console.log(chalk.blue(`📊 API Health: ${health.rateLimit} requests used`));
      } else {
        console.log(chalk.red('❌ GRID API connection failed'));
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red('❌ API test failed:'), error);
      process.exit(1);
    }
  });

program
  .command('dates')
  .description('Show available date options')
  .action(() => {
    console.log(chalk.blue.bold('📅 Available Date Options:'));
    console.log('');

    const options = DateManager.getDateOptions();

    console.log(chalk.green('TODAY:'));
    console.log(`  Start: ${DateManager.formatDisplayDate(options.TODAY.start)}`);
    console.log(`  End:   ${DateManager.formatDisplayDate(options.TODAY.end)}`);
    console.log('');

    console.log(chalk.green('TOMORROW:'));
    console.log(`  Start: ${DateManager.formatDisplayDate(options.TOMORROW.start)}`);
    console.log(`  End:   ${DateManager.formatDisplayDate(options.TOMORROW.end)}`);
    console.log('');

    console.log(chalk.green('CUSTOM:'));
    console.log('  Use --start and --end parameters with YYYY-MM-DD format');
    console.log('  Example: --date CUSTOM --start 2024-01-15 --end 2024-01-16');
  });

async function parseOptions(options: any): Promise<AnalysisConfig> {
  const config: AnalysisConfig = { ...DEFAULT_CONFIG };

  // Parse date selection
  const dateUpper = options.date.toUpperCase();
  if (!['TODAY', 'TOMORROW', 'CUSTOM'].includes(dateUpper)) {
    throw new Error(`Invalid date selection: ${options.date}`);
  }
  config.dateSelection = dateUpper as DateSelection;

  // Parse numeric options
  config.minGamesThreshold = parseInt(options.minGames);
  config.confidenceThreshold = parseInt(options.confidence);

  // Parse risk tier
  const riskUpper = options.maxRisk.toUpperCase();
  if (!['LOW', 'MEDIUM', 'HIGH'].includes(riskUpper)) {
    throw new Error(`Invalid risk tier: ${options.maxRisk}`);
  }
  config.maxRiskTier = riskUpper as any;

  // Parse boolean options
  config.includePlayerAnalysis = !options.noPlayers;
  config.includeMapAnalysis = options.includeMaps;

  return config;
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ Uncaught Exception:'), error);
  process.exit(1);
});

program.parse();
