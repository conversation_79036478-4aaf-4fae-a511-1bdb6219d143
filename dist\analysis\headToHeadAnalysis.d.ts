import { CS2Series, HeadToHeadAnalysis, Connection } from '../types';
export declare class HeadToHeadAnalyzer {
    /**
     * Analyze head-to-head record between two teams
     */
    static analyzeHeadToHead(team1Id: string, team2Id: string, team1Name: string, team2Name: string, seriesData: Connection<CS2Series>): HeadToHeadAnalysis;
    /**
     * Analyze series win/loss record
     */
    private static analyzeSeriesRecord;
    /**
     * Analyze individual map win/loss record
     */
    private static analyzeMapRecord;
    /**
     * Calculate average score differential per map
     */
    private static calculateAverageScoreDifferential;
    /**
     * Get recent encounters with details
     */
    private static getRecentEncounters;
    /**
     * Get series score string
     */
    private static getSeriesScore;
    /**
     * Get empty analysis for when no data is available
     */
    private static getEmptyAnalysis;
    /**
     * Calculate head-to-head advantage
     */
    static calculateH2HAdvantage(analysis: HeadToHeadAnalysis): {
        team1Advantage: number;
        team2Advantage: number;
        isSignificant: boolean;
    };
    /**
     * Get map-specific head-to-head for a particular map
     */
    static getMapSpecificH2H(team1Id: string, team2Id: string, mapId: string, series: CS2Series[]): {
        team1Wins: number;
        team2Wins: number;
        totalMaps: number;
    };
    /**
     * Analyze recent form trend from head-to-head
     */
    static analyzeRecentFormTrend(team1Id: string, team2Id: string, series: CS2Series[]): {
        team1Trend: 'UP' | 'DOWN' | 'STABLE';
        team2Trend: 'UP' | 'DOWN' | 'STABLE';
    };
    /**
     * Determine trend based on recent vs older performance
     */
    private static determineTrend;
}
//# sourceMappingURL=headToHeadAnalysis.d.ts.map