import { CS2Series, HeadToHeadAnalysis, Connection } from '../types';
export declare class HeadToHeadAnalyzer {
    static analyzeHeadToHead(team1Id: string, team2Id: string, team1Name: string, team2Name: string, seriesData: Connection<CS2Series>): HeadToHeadAnalysis;
    private static analyzeSeriesRecord;
    private static analyzeMapRecord;
    private static calculateAverageScoreDifferential;
    private static getRecentEncounters;
    private static getSeriesScore;
    private static getEmptyAnalysis;
    static calculateH2HAdvantage(analysis: HeadToHeadAnalysis): {
        team1Advantage: number;
        team2Advantage: number;
        isSignificant: boolean;
    };
    static getMapSpecificH2H(team1Id: string, team2Id: string, mapId: string, series: CS2Series[]): {
        team1Wins: number;
        team2Wins: number;
        totalMaps: number;
    };
    static analyzeRecentFormTrend(team1Id: string, team2Id: string, series: CS2Series[]): {
        team1Trend: 'UP' | 'DOWN' | 'STABLE';
        team2Trend: 'UP' | 'DOWN' | 'STABLE';
    };
    private static determineTrend;
}
//# sourceMappingURL=headToHeadAnalysis.d.ts.map