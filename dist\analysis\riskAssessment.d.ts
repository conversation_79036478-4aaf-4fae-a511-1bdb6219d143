import { RiskTier, TeamDominanceScore, BettingRecommendation } from '../types';
export interface RiskCriteria {
    tdsGap: number;
    recentFormWinRate: number;
    dataQualityPercent: number;
    gamesSample: number;
    confidenceThreshold: number;
}
export interface RiskTierDefinition {
    tier: RiskTier;
    criteria: RiskCriteria;
    description: string;
    emoji: string;
}
export declare class RiskAssessment {
    private static readonly RISK_TIERS;
    /**
     * Assess risk tier for a betting opportunity
     */
    static assessRiskTier(team1TDS: TeamDominanceScore, team2TDS: TeamDominanceScore, recentForm: {
        team1WinRate: number;
        team2WinRate: number;
    }, dataQuality: {
        realDataPercent: number;
        sampleSize: {
            team1: number;
            team2: number;
        };
    }): RiskTier;
    /**
     * Check if betting opportunity should be automatically excluded
     */
    private static shouldExclude;
    /**
     * Check if criteria are met for a specific risk tier
     */
    private static meetsCriteria;
    /**
     * Convert confidence level to numeric score
     */
    private static getMinConfidenceScore;
    /**
     * Generate betting recommendation based on risk assessment
     */
    static generateBettingRecommendation(team1Name: string, team2Name: string, team1TDS: TeamDominanceScore, team2TDS: TeamDominanceScore, odds: {
        team1: number;
        team2: number;
    }, riskTier: RiskTier): BettingRecommendation | null;
    /**
     * Calculate overall confidence score
     */
    private static calculateOverallConfidence;
    /**
     * Convert confidence level to numeric score
     */
    private static confidenceToScore;
    /**
     * Calculate expected value
     */
    private static calculateExpectedValue;
    /**
     * Calculate Kelly criterion stake
     */
    private static calculateKellyStake;
    /**
     * Get maximum risk percentage for tier
     */
    private static getMaxRiskForTier;
    /**
     * Get risk tier definition
     */
    static getRiskTierDefinition(tier: RiskTier): RiskTierDefinition | null;
    /**
     * Get all risk tier definitions
     */
    static getAllRiskTiers(): RiskTierDefinition[];
    /**
     * Validate risk assessment parameters
     */
    static validateRiskParameters(team1TDS: TeamDominanceScore, team2TDS: TeamDominanceScore, dataQuality: {
        realDataPercent: number;
        sampleSize: {
            team1: number;
            team2: number;
        };
    }): {
        isValid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=riskAssessment.d.ts.map