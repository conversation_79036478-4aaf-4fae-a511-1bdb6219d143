import { RiskTier, TeamDominanceScore, BettingRecommendation } from '../types';
export interface RiskCriteria {
    tdsGap: number;
    recentFormWinRate: number;
    dataQualityPercent: number;
    gamesSample: number;
    confidenceThreshold: number;
}
export interface RiskTierDefinition {
    tier: RiskTier;
    criteria: RiskCriteria;
    description: string;
    emoji: string;
}
export declare class RiskAssessment {
    private static readonly RISK_TIERS;
    static assessRiskTier(team1TDS: TeamDominanceScore, team2TDS: TeamDominanceScore, recentForm: {
        team1WinRate: number;
        team2WinRate: number;
    }, dataQuality: {
        realDataPercent: number;
        sampleSize: {
            team1: number;
            team2: number;
        };
    }): RiskTier;
    private static shouldExclude;
    private static meetsCriteria;
    private static getMinConfidenceScore;
    static generateBettingRecommendation(team1Name: string, team2Name: string, team1TDS: TeamDominanceScore, team2TDS: TeamDominanceScore, odds: {
        team1: number;
        team2: number;
    }, riskTier: RiskTier): BettingRecommendation | null;
    private static calculateOverallConfidence;
    private static confidenceToScore;
    private static calculateExpectedValue;
    private static calculateKellyStake;
    private static getMaxRiskForTier;
    static getRiskTierDefinition(tier: RiskTier): RiskTierDefinition | null;
    static getAllRiskTiers(): RiskTierDefinition[];
    static validateRiskParameters(team1TDS: TeamDominanceScore, team2TDS: TeamDominanceScore, dataQuality: {
        realDataPercent: number;
        sampleSize: {
            team1: number;
            team2: number;
        };
    }): {
        isValid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=riskAssessment.d.ts.map