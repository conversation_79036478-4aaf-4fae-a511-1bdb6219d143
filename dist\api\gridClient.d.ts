import { Connection, CS2Series, CS2TeamStats, CS2PlayerStats, DateRange } from '../types';
export declare class GridAPIClient {
    private client;
    private apiKey;
    private baseURL;
    private requestCount;
    private lastRequestTime;
    private readonly RATE_LIMIT;
    private readonly RATE_LIMIT_WINDOW;
    constructor(apiKey: string);
    private enforceRateLimit;
    private makeRequest;
    getCS2Teams(searchTerm?: string): Promise<Connection<any>>;
    getCS2Matches(dateFilter: {
        gte: string;
        lte: string;
    }): Promise<Connection<CS2Series>>;
    getTeamStats(teamId: string, dateRange: DateRange): Promise<CS2TeamStats>;
    getPlayerStats(playerId: string, dateRange: DateRange): Promise<CS2PlayerStats>;
    getHeadToHead(team1Id: string, team2Id: string, dateRange: DateRange): Promise<Connection<CS2Series>>;
    getMapStats(teamId: string, mapId: string, dateRange: DateRange): Promise<CS2TeamStats>;
    getAPIHealth(): {
        responseTime: number;
        rateLimit: string;
        requestCount: number;
    };
    validateConnection(): Promise<boolean>;
}
//# sourceMappingURL=gridClient.d.ts.map