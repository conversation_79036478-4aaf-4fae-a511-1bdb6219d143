"use strict";
/**
 * CS2 Betting Analysis Framework
 * Enhanced Prediction with GRID API Integration
 *
 * This is the main entry point for the CS2 betting analysis system.
 * It provides programmatic access to the analysis engine for integration
 * with other systems or custom implementations.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PredictionFormatter = exports.DateManager = exports.GridAPIClient = exports.HeadToHeadAnalyzer = exports.RiskAssessment = exports.StatisticalEngine = exports.AnalysisEngine = void 0;
exports.analyzeCS2Matches = analyzeCS2Matches;
exports.getDetailedAnalysis = getDetailedAnalysis;
var analysisEngine_1 = require("./analysis/analysisEngine");
Object.defineProperty(exports, "AnalysisEngine", { enumerable: true, get: function () { return analysisEngine_1.AnalysisEngine; } });
var statisticalEngine_1 = require("./analysis/statisticalEngine");
Object.defineProperty(exports, "StatisticalEngine", { enumerable: true, get: function () { return statisticalEngine_1.StatisticalEngine; } });
var riskAssessment_1 = require("./analysis/riskAssessment");
Object.defineProperty(exports, "RiskAssessment", { enumerable: true, get: function () { return riskAssessment_1.RiskAssessment; } });
var headToHeadAnalysis_1 = require("./analysis/headToHeadAnalysis");
Object.defineProperty(exports, "HeadToHeadAnalyzer", { enumerable: true, get: function () { return headToHeadAnalysis_1.HeadToHeadAnalyzer; } });
var gridClient_1 = require("./api/gridClient");
Object.defineProperty(exports, "GridAPIClient", { enumerable: true, get: function () { return gridClient_1.GridAPIClient; } });
var dateUtils_1 = require("./utils/dateUtils");
Object.defineProperty(exports, "DateManager", { enumerable: true, get: function () { return dateUtils_1.DateManager; } });
var predictionFormatter_1 = require("./output/predictionFormatter");
Object.defineProperty(exports, "PredictionFormatter", { enumerable: true, get: function () { return predictionFormatter_1.PredictionFormatter; } });
// Export all types
__exportStar(require("./types"), exports);
// Example usage function
const analysisEngine_2 = require("./analysis/analysisEngine");
const predictionFormatter_2 = require("./output/predictionFormatter");
/**
 * Quick analysis function for easy integration
 */
async function analyzeCS2Matches(apiKey, dateSelection = 'TODAY', options = {}) {
    const config = {
        dateSelection,
        minGamesThreshold: 5,
        confidenceThreshold: 65,
        maxRiskTier: 'HIGH',
        includePlayerAnalysis: true,
        includeMapAnalysis: false,
        ...options
    };
    const engine = new analysisEngine_2.AnalysisEngine(apiKey, config);
    const analyses = await engine.analyzeMatches(dateSelection);
    if (analyses.length === 0) {
        return 'No betting opportunities found for the specified criteria.';
    }
    // Return formatted summary
    return predictionFormatter_2.PredictionFormatter.formatTopMatchesSummary(analyses, dateSelection);
}
/**
 * Get detailed analysis for a specific date range
 */
async function getDetailedAnalysis(apiKey, dateSelection = 'TODAY', customDates) {
    const config = {
        dateSelection,
        minGamesThreshold: 5,
        confidenceThreshold: 65,
        maxRiskTier: 'HIGH',
        includePlayerAnalysis: true,
        includeMapAnalysis: false
    };
    const engine = new analysisEngine_2.AnalysisEngine(apiKey, config);
    const analyses = await engine.analyzeMatches(dateSelection, customDates);
    return analyses.map(analysis => predictionFormatter_2.PredictionFormatter.formatMatchAnalysis(analysis, dateSelection));
}
// Default export for convenience
exports.default = {
    analyzeCS2Matches,
    getDetailedAnalysis,
    AnalysisEngine: analysisEngine_2.AnalysisEngine,
    PredictionFormatter: predictionFormatter_2.PredictionFormatter
};
//# sourceMappingURL=index.js.map