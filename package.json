{"name": "cs2-betting-analysis", "version": "1.0.0", "description": "CS2 Betting Analysis Framework with GRID API Integration", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "analyze": "ts-node src/cli.ts", "demo": "ts-node src/demo.ts", "test": "jest", "lint": "eslint src/**/*.ts"}, "keywords": ["cs2", "betting", "analysis", "esports", "grid-api"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "moment": "^2.29.4", "graphql": "^16.8.1", "graphql-request": "^6.1.0", "commander": "^11.1.0", "chalk": "^4.1.2", "table": "^6.8.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}}