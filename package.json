{"name": "cs2-betting-analysis", "version": "1.0.0", "description": "CS2 Betting Analysis Framework with Real HLTV Data Integration", "main": "dist/index.js", "bin": {"cs2-betting-analysis": "./dist/cli.js"}, "scripts": {"build": "tsc", "start": "node dist/cli.js", "dev": "ts-node src/cli.ts", "analyze": "ts-node src/cli.ts", "hltv": "ts-node src/cli.ts hltv", "test-hltv": "ts-node src/cli.ts test-hltv", "clean": "<PERSON><PERSON><PERSON> dist", "watch": "tsc --watch"}, "keywords": ["cs2", "counter-strike", "betting", "analysis", "hltv", "esports", "prediction"], "author": "CS2 Betting Analysis Team", "license": "MIT", "dependencies": {"@types/moment": "^2.11.29", "axios": "^1.7.7", "chalk": "^4.1.2", "cheerio": "^1.0.0", "commander": "^12.1.0", "graphql-request": "^6.1.0", "moment": "^2.30.1"}, "devDependencies": {"@types/node": "^22.10.2", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "engines": {"node": ">=18.0.0"}}