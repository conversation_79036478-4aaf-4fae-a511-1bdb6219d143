GraphQL
Authentication

Users of all the GRID APIs must identify themselves before consuming the APIs. The authentication process requires passing a key to the API.

When consuming a GraphQL API, the API key can be passed by setting the

x-api-key

header in the HTTP request. The way in which this is achieved will vary depending on the specific tool being utilized.

More information about the GraphQL authorization mechanism can be found in the official documentation located at this page.
Pagination

GRID APIs can be used to retrieve all the series for a given time period. While querying for short time periods may yield a small list of series in the response, querying for larger periods, such as a year, would result in larger lists of series. In this latter case, pagination is used to split the results into multiple pages.

GRID APIs maximum page length is 50 results. Thus, a single query will return no more than 50 series. The caller can adjust the page length, and it is currently set to 10 results by default.

Let us consider the following query, which can be found in the GRID Playground. The query requests all the series that started between 03/11/2021 and 04/11/2021, and it will yield the first 10 series from that period.

query GetAllSeriesInNext24Hours {

  allSeries(

    filter:{

      startTimeScheduled:{

        gte: "2021-11-03T00:00:00+01:00"

        lte: "2021-11-04T00:00:00+01:00"

      }

    }

    orderBy: StartTimeScheduled

  ) {

    totalCount,

    pageInfo{

      hasPreviousPage

      hasNextPage

      startCursor

      endCursor

    }

    edges{

      cursor

      node{

        id

        startTimeScheduled

      }

    }

  }

}

The resulting JSON response can be similar to the following:

{

  "data": {

    "allSeries": {

      "totalCount": 32,

      "pageInfo": {

        "hasPreviousPage": false,

        "hasNextPage": true,

        "startCursor": "JAMWBQMjHhoSJBQfEhMCGxITCwtaCwtERUdORUcLC1oLC0VHRUZaRkZaR0QjR05NR0dNR0ct",

        "endCursor": "JAMWBQMjHhoSJBQfEhMCGxITCwtaCwtERUZHRUULC1oLC0VHRUZaRkZaR0QjRkVNR0dNR0ct"

      },

      "edges": [...]

    }

  }

}


To understand this response, it is necessary to examine the following three fields values:

    totalCount refers to the number of results matching the given filter.
    hasNextPage indicates whether further querying is required to obtain all the results.
    endCursor works like a bookmark. It indicates where further queries should start in order to get the next set of results.

Based on the above information, the following query can be executed in order to obtain the next set of results:

query GetAllSeriesInNext24Hours {

  allSeries(

    after: "JAMWBQMjHhoSJBQfEhMCGxITCwtaCwtERUZHRUULC1oLC0VHRUZaRkZaR0QjRkVNR0dNR0ct"

    filter:{

      startTimeScheduled:{

        gte: "2021-11-03T00:00:00+01:00"

        lte: "2021-11-04T00:00:00+01:00"

      }

    }

    orderBy: StartTimeScheduled

  ) {

    totalCount,

    pageInfo{

      hasPreviousPage

      hasNextPage

      startCursor

      endCursor

    }

    edges{

      cursor

      node{

        id

        startTimeScheduled

      }

    }

  }

}

Notably, the

after

field is set with the value of the

endCursor

field from the result of the previous query. This ensures that the first item of the returned result is the one following the last of the first query. In order to fetch the entire result set, it is sufficient to keep executing queries, passing the value of the

endCursor

of the previous result, until the

hasNextPage

field is set to

false

.

Instead, in order to get all the results in one query - but always up to 50 - it would be sufficient to specify the

first

field in the query:

query GetAllSeriesInNext24Hours {

  allSeries(

    first: 50

    filter:{

      startTimeScheduled:{

        gte: "2021-11-03T09:05:39+01:00"

        lte: "2021-11-04T09:05:39+01:00"

      }

    }

    orderBy: StartTimeScheduled

  ) {

    totalCount,

    pageInfo{

      hasPreviousPage

      hasNextPage

      startCursor

      endCursor

    }

    edges{

      cursor

      node{

        id

        startTimeScheduled

      }

    }

  }

}

The

first

field indicates the number of items we request. This can be any number, from 1 to 50. The

first

field has a counterpart:

last

, which allows querying the results the other way around; instead of returning the first X results that match the given filter, the result will include the last X results.

More information on the GraphQL pagination can be found in the official documentation here.
Rate limit

The rate limit restricts the number of queries a user can execute in a time period.

By default, GRID allows 40 requests per minute for Closed Platform and 20 requests per minute for Open Platform. Any more attempts will be rejected.
Errors
Error type
	
Reason
	
What to do
UNAUTHENTICATED
	
The API key provided can’t be found.
	
Check the right API key is passed. If the API key is correct, users may contact the GRID Support team for assistance.
UNAUTHORIZED
	
The API key provided has no access to the product/content.
	
Contact the GRID Support team to get the proper permissions.
Error type
	
Error detail
	
Reason
	
What to do
BAD_REQUEST
	
INVALID_ARGUMENT
	
Can either provide first/after OR last/before
	
When paginating, make sure not to have
BAD_REQUEST
	
INVALID_ARGUMENT
	
from
	
Make sure that the end date for the time window is larger than the start date.
BAD_REQUEST
	
INVALID_ARGUMENT
	
Parameter cannot be used as DateTime
	
Make sure the provided date is in the
BAD_REQUEST
	
INVALID_ARGUMENT
	
Invalid cursor value provided
	
Make sure the provided cursor value is the right one.
BAD_REQUEST
	
INVALID_ARGUMENT
	
Invalid {titleId/tournamentId/seriesId/teamId} value provided
	
The provided entity ID provided does not exist on Central Data.
Example responses
UNAUTHENTICATED

{

  "errors": [

    {

      "message": "Requester unauthorized to make query",

      "extensions": {

        "code": "DOWNSTREAM_SERVICE_ERROR",

        "serviceName": "gql-cd-internal-query",

        "errorType": "UNAUTHENTICATED",

        "exception": {

          "message": "Requester unauthorized to make query",

          "locations": [],

          "path": [

            "queryValue"

          ]

        }

      }

    }

  ],

  "data": {

    "title": null

  }

}


UNAUTHORIZED

{

  "errors": [

    {

      "message": "Requester forbidden to make query",

      "extensions": {

        "code": "DOWNSTREAM_SERVICE_ERROR",

        "serviceName": "gql-cd-internal-query",

        "errorType": "PERMISSION_DENIED",

        "exception": {

          "message": "Requester forbidden to make query",

          "locations": [],

          "path": [

            "queryValue"

          ]

        }

      }

    }

  ],

  "data": {

    "title": null

  }

}


BAD_REQUEST

{

  "errors": [

    {

      "message": "Parameter cannot be used as DateTime",

      "extensions": {

        "code": "DOWNSTREAM_SERVICE_ERROR",

        "serviceName": "gql-cd-internal-query",

        "errorType": "BAD_REQUEST",

        "errorDetail": "INVALID_ARGUMENT",

        "exception": {

          "message": "Parameter cannot be used as DateTime",

          "locations": [],

          "path": [

            "allSeries"

          ]

        }

      }

    }

  ],

  "data": null

}
