import { CS2TeamStats, CS2PlayerStats, TeamDominanceScore, PlayerImpactRating, MultiKills } from '../types';
export declare class StatisticalEngine {
    /**
     * Calculate Team Dominance Score (TDS) based on real GRID API data
     */
    static calculateTDS(teamStats: CS2TeamStats, teamName: string): TeamDominanceScore;
    /**
     * Calculate Player Impact Rating (PIR) based on verified real data
     */
    static calculatePIR(playerStats: CS2PlayerStats, playerName: string): PlayerImpactRating | null;
    /**
     * Calculate clutch success rate from clutch statistics
     */
    private static calculateClutchRate;
    /**
     * Normalize K/D ratio to 0-1 scale
     */
    private static normalizeKDR;
    /**
     * Normalize ADR to 0-1 scale
     */
    private static normalizeADR;
    /**
     * Normalize rating to 0-1 scale
     */
    private static normalizeRating;
    /**
     * Calculate confidence level based on data quality
     */
    private static calculateConfidence;
    /**
     * Calculate multi-kill impact score
     */
    static calculateMultiKillImpact(multiKills: MultiKills): number;
    /**
     * Calculate team synergy score based on individual player ratings
     */
    static calculateTeamSynergy(playerRatings: PlayerImpactRating[]): number;
    /**
     * Calculate variance of an array of numbers
     */
    private static calculateVariance;
    /**
     * Calculate momentum score based on recent performance trend
     */
    static calculateMomentum(recentWins: number, totalRecentGames: number): number;
    /**
     * Validate statistical significance of data
     */
    static validateStatisticalSignificance(teamStats: CS2TeamStats, minGames?: number): {
        isValid: boolean;
        reason?: string;
    };
    /**
     * Calculate expected value for betting recommendation
     */
    static calculateExpectedValue(winProbability: number, odds: number, confidence: number): number;
    /**
     * Calculate Kelly criterion stake size
     */
    static calculateKellyStake(winProbability: number, odds: number, confidence: number, maxStake?: number): number;
}
//# sourceMappingURL=statisticalEngine.d.ts.map