import { CS2TeamStats, CS2PlayerStats, TeamDominanceScore, PlayerImpactRating, MultiKills } from '../types';
export declare class StatisticalEngine {
    static calculateTDS(teamStats: CS2TeamStats, teamName: string): TeamDominanceScore;
    static calculatePIR(playerStats: CS2PlayerStats, playerName: string): PlayerImpactRating | null;
    private static calculateClutchRate;
    private static normalizeKDR;
    private static normalizeADR;
    private static normalizeRating;
    private static calculateConfidence;
    static calculateMultiKillImpact(multiKills: MultiKills): number;
    static calculateTeamSynergy(playerRatings: PlayerImpactRating[]): number;
    private static calculateVariance;
    static calculateMomentum(recentWins: number, totalRecentGames: number): number;
    static validateStatisticalSignificance(teamStats: CS2TeamStats, minGames?: number): {
        isValid: boolean;
        reason?: string;
    };
    static calculateExpectedValue(winProbability: number, odds: number, confidence: number): number;
    static calculateKellyStake(winProbability: number, odds: number, confidence: number, maxStake?: number): number;
}
//# sourceMappingURL=statisticalEngine.d.ts.map