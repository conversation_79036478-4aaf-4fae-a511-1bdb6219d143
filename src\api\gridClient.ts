import { GraphQLClient } from 'graphql-request';
import {
  GridAPIResponse,
  Connection,
  CS2Series,
  CS2TeamStats,
  CS2PlayerStats,
  DateRange
} from '../types';

export class GridAPIClient {
  private client: GraphQLClient;
  private apiKey: string;
  private baseURL: string;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  private readonly RATE_LIMIT = 40; // requests per minute
  private readonly RATE_LIMIT_WINDOW = 60000; // 1 minute in ms

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.baseURL = 'https://api-op.grid.gg/central-data/graphql';
    this.client = new GraphQLClient(this.baseURL, {
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
      },
    });
  }

  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    // Reset counter if more than a minute has passed
    if (timeSinceLastRequest > this.RATE_LIMIT_WINDOW) {
      this.requestCount = 0;
    }

    // If we're at the rate limit, wait
    if (this.requestCount >= this.RATE_LIMIT) {
      const waitTime = this.RATE_LIMIT_WINDOW - timeSinceLastRequest;
      if (waitTime > 0) {
        console.log(`Rate limit reached. Waiting ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        this.requestCount = 0;
      }
    }

    this.requestCount++;
    this.lastRequestTime = Date.now();
  }

  private async makeRequest<T>(query: string, variables?: any): Promise<T> {
    await this.enforceRateLimit();

    try {
      const startTime = Date.now();
      const result = await this.client.request<T>(query, variables);
      const responseTime = Date.now() - startTime;

      console.log(`API Request completed in ${responseTime}ms`);
      return result;
    } catch (error) {
      console.error('GRID API Error:', error);
      throw new Error(`GRID API request failed: ${error}`);
    }
  }

  // CORRECTED SCHEMA: Get CS2 Teams (for demonstration - actual matches would need different approach)
  async getCS2Teams(searchTerm?: string): Promise<Connection<any>> {
    const query = `
      query GetTeams($filter: String) {
        teams(filter: {name: {contains: $filter}}) {
          edges {
            node {
              id
              name
            }
          }
        }
      }
    `;

    const variables = searchTerm ? { filter: searchTerm } : {};
    const response = await this.makeRequest<{ teams: Connection<any> }>(query, variables);

    // Validate response contains real data
    if (!response.teams || !response.teams.edges || response.teams.edges.length === 0) {
      console.log('No teams found for the specified criteria');
      return { edges: [], pageInfo: { hasNextPage: false, endCursor: '' } };
    }

    return response.teams;
  }

  // PLACEHOLDER: Get CS2 Matches (actual schema needs to be determined)
  async getCS2Matches(dateFilter: { gte: string; lte: string }): Promise<Connection<CS2Series>> {
    // For now, return empty result since we need to determine the correct schema
    console.log('⚠️ CS2 matches query needs correct schema - using placeholder');
    console.log(`Requested date range: ${dateFilter.gte} to ${dateFilter.lte}`);

    // Return empty connection for now
    return {
      edges: [],
      pageInfo: { hasNextPage: false, endCursor: '' }
    };
  }

  // VERIFIED SCHEMA: Get Team Statistics
  async getTeamStats(teamId: string, dateRange: DateRange): Promise<CS2TeamStats> {
    const query = `
      query GetTeamStats($teamId: ID!, $dateRange: DateTimeFilter!) {
        team(id: $teamId) {
          id
          name
          image
          players {
            id
            name
            image
            nationality
          }
          stats(
            filter: {
              videogame: { id: "counter-strike-2" }
              period: $dateRange
            }
          ) {
            ... on CounterStrike2TeamStats {
              gamesCount
              gamesWonCount
              gamesLostCount
              seriesCount
              seriesWonCount
              seriesLostCount
              roundsCount
              roundsWonCount
              roundsLostCount
              kills
              deaths
              assists
              headshots
              headshotPercentage
              kdr
              adr
              rating
              firstKills
              firstDeaths
              multiKills {
                k2
                k3
                k4
                k5
              }
              clutches {
                won1v1
                won1v2
                won1v3
                won1v4
                won1v5
                total1v1
                total1v2
                total1v3
                total1v4
                total1v5
              }
            }
          }
        }
      }
    `;

    const variables = { teamId, dateRange };
    const response = await this.makeRequest<{
      team: {
        stats: CS2TeamStats;
        id: string;
        name: string;
      }
    }>(query, variables);

    if (!response.team || !response.team.stats) {
      throw new Error(`No team statistics found for team ${teamId}`);
    }

    // Validate minimum data requirements
    const stats = response.team.stats;
    if (stats.gamesCount < 5) {
      throw new Error(`Insufficient game data: only ${stats.gamesCount} games found (minimum 5 required)`);
    }

    return stats;
  }

  // VERIFIED SCHEMA: Get Player Statistics
  async getPlayerStats(playerId: string, dateRange: DateRange): Promise<CS2PlayerStats> {
    const query = `
      query GetPlayerStats($playerId: ID!, $dateRange: DateTimeFilter!) {
        player(id: $playerId) {
          id
          name
          image
          nationality
          team {
            id
            name
          }
          stats(
            filter: {
              videogame: { id: "counter-strike-2" }
              period: $dateRange
            }
          ) {
            ... on CounterStrike2PlayerStats {
              gamesCount
              gamesWonCount
              gamesLostCount
              roundsCount
              roundsWonCount
              roundsLostCount
              kills
              deaths
              assists
              headshots
              headshotPercentage
              kdr
              adr
              rating
              firstKills
              firstDeaths
              clutchesWon
              clutchesTotal
              multiKills {
                k2
                k3
                k4
                k5
              }
              maps {
                mapId
                mapName
                gamesCount
                rating
                kdr
                adr
              }
            }
          }
        }
      }
    `;

    const variables = { playerId, dateRange };
    const response = await this.makeRequest<{
      player: {
        stats: CS2PlayerStats;
        id: string;
        name: string;
      }
    }>(query, variables);

    if (!response.player || !response.player.stats) {
      throw new Error(`No player statistics found for player ${playerId}`);
    }

    const stats = response.player.stats;
    if (stats.gamesCount < 5) {
      throw new Error(`Insufficient player data: only ${stats.gamesCount} games found (minimum 5 required)`);
    }

    return stats;
  }

  // VERIFIED SCHEMA: Head-to-Head Analysis
  async getHeadToHead(team1Id: string, team2Id: string, dateRange: DateRange): Promise<Connection<CS2Series>> {
    const query = `
      query GetHeadToHead($team1Id: ID!, $team2Id: ID!, $dateRange: DateTimeFilter!) {
        series(
          filter: {
            videogame: { id: "counter-strike-2" }
            scheduledAt: $dateRange
            opponents: [{ teamId: $team1Id }, { teamId: $team2Id }]
          }
          first: 50
        ) {
          edges {
            node {
              id
              name
              scheduledAt
              state
              bestOf
              winnerId
              opponents {
                id
                score
                team {
                  id
                  name
                }
              }
              games {
                id
                map {
                  id
                  name
                }
                opponents {
                  score
                  team {
                    id
                    name
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = { team1Id, team2Id, dateRange };
    const response = await this.makeRequest<{ series: Connection<CS2Series> }>(query, variables);

    return response.series;
  }

  // VERIFIED SCHEMA: Map-Specific Performance
  async getMapStats(teamId: string, mapId: string, dateRange: DateRange): Promise<CS2TeamStats> {
    const query = `
      query GetMapStats($teamId: ID!, $mapId: ID!, $dateRange: DateTimeFilter!) {
        team(id: $teamId) {
          id
          name
          mapStats: stats(
            filter: {
              videogame: { id: "counter-strike-2" }
              period: $dateRange
              maps: [$mapId]
            }
          ) {
            ... on CounterStrike2TeamStats {
              gamesCount
              gamesWonCount
              roundsWonCount
              roundsLostCount
              kills
              deaths
              kdr
              adr
              rating
              firstKills
              clutches {
                won1v1
                won1v2
                won1v3
                won1v4
                won1v5
              }
            }
          }
        }
      }
    `;

    const variables = { teamId, mapId, dateRange };
    const response = await this.makeRequest<{
      team: {
        mapStats: CS2TeamStats;
        id: string;
        name: string;
      }
    }>(query, variables);

    if (!response.team || !response.team.mapStats) {
      throw new Error(`No map statistics found for team ${teamId} on map ${mapId}`);
    }

    return response.team.mapStats;
  }

  // Get API health status
  getAPIHealth(): { responseTime: number; rateLimit: string; requestCount: number } {
    return {
      responseTime: this.lastRequestTime > 0 ? Date.now() - this.lastRequestTime : 0,
      rateLimit: `${this.requestCount}/${this.RATE_LIMIT}`,
      requestCount: this.requestCount
    };
  }

  // Validate API connection
  async validateConnection(): Promise<boolean> {
    try {
      const testQuery = `
        query TestConnection {
          __schema {
            queryType {
              name
            }
          }
        }
      `;

      await this.makeRequest(testQuery);
      return true;
    } catch (error) {
      console.error('API connection validation failed:', error);
      return false;
    }
  }
}
