import { DateRange, DateOptions, DateSelection } from '../types';
export declare class DateManager {
    /**
     * Get date ranges for different analysis periods based on user selection
     */
    static getAnalysisDateRanges(selectedOption: DateSelection, customDates?: {
        start: string;
        end: string;
    }): {
        matchDate: DateRange;
        teamForm: DateRange;
        headToHead: DateRange;
        playerConsistency: DateRange;
    };
    /**
     * Get match date range based on selection option
     */
    static getMatchDateRange(selectedOption: DateSelection, customDates?: {
        start: string;
        end: string;
    }): DateRange;
    /**
     * Get all available date options with current UTC timestamps
     */
    static getDateOptions(): DateOptions;
    /**
     * Validate date range is valid
     */
    static validateDateRange(dateRange: DateRange): boolean;
    /**
     * Check if data is fresh (within specified hours)
     */
    static isDataFresh(lastUpdate: string, maxAgeHours?: number): boolean;
    /**
     * Calculate days since last match
     */
    static daysSinceLastMatch(lastMatchDate: string): number;
    /**
     * Format date for display
     */
    static formatDisplayDate(date: string): string;
    /**
     * Get current UTC timestamp
     */
    static getCurrentUTC(): string;
    /**
     * Parse custom date input and validate
     */
    static parseCustomDate(dateInput: string): string;
    /**
     * Get time until match starts
     */
    static getTimeUntilMatch(scheduledAt: string): {
        hours: number;
        minutes: number;
        isLive: boolean;
        isPast: boolean;
    };
    /**
     * Check if match is in peak performance window
     */
    static isInPeakPerformanceWindow(scheduledAt: string): boolean;
    /**
     * Calculate schedule intensity (matches in last 7 days)
     */
    static calculateScheduleIntensity(recentMatches: string[]): number;
}
//# sourceMappingURL=dateUtils.d.ts.map