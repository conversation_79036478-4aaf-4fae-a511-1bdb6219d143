import { DateRange, DateOptions, DateSelection } from '../types';
export declare class DateManager {
    static getAnalysisDateRanges(selectedOption: DateSelection, customDates?: {
        start: string;
        end: string;
    }): {
        matchDate: DateRange;
        teamForm: DateRange;
        headToHead: DateRange;
        playerConsistency: DateRange;
    };
    static getMatchDateRange(selectedOption: DateSelection, customDates?: {
        start: string;
        end: string;
    }): DateRange;
    static getDateOptions(): DateOptions;
    static validateDateRange(dateRange: DateRange): boolean;
    static isDataFresh(lastUpdate: string, maxAgeHours?: number): boolean;
    static daysSinceLastMatch(lastMatchDate: string): number;
    static formatDisplayDate(date: string): string;
    static getCurrentUTC(): string;
    static parseCustomDate(dateInput: string): string;
    static getTimeUntilMatch(scheduledAt: string): {
        hours: number;
        minutes: number;
        isLive: boolean;
        isPast: boolean;
    };
    static isInPeakPerformanceWindow(scheduledAt: string): boolean;
    static calculateScheduleIntensity(recentMatches: string[]): number;
}
//# sourceMappingURL=dateUtils.d.ts.map