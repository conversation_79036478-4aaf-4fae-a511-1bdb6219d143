Cs2PlayerSeriesStatistics

The CSGO player statistics for aggregated series.
Field
	
Type
	
Description
count
	
Int!
	
The number of aggregated series.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the player.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the player.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the player.
teamkills
	
AggregateIntStatistic!
	
The aggregations of player kills acquired by the player.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kills given by the player.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kills received by the player.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the player.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the player.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the player.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the player.
wins
	
[BooleanOccurrenceStatistic]!
	
Use won instead
won
	
[BooleanOccurrenceStatistic]!
	
The player's series' wins statistics.
objectives
	
[ObjectiveStatistics!]!
	
The player's accomplished objectives.
firstKill
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the player first kills.
headshots
	
AggregateIntStatistic!
	
The aggregations of headshots acquired by the player.

Cs2TeamSeriesStatistics

The CSGO team statistics for aggregated series.
Field
	
Type
	
Description
count
	
Int!
	
The number of aggregated series.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the team.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the team.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the team.
teamkills
	
AggregateIntStatistic!
	
The aggregations of team kills acquired by the team.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kills given by the team.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kills received by the team.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the team.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the team.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the team.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the team.
score
	
AggregateIntStatistic!
	
The aggregations of team's score.
wins
	
[BooleanOccurrenceStatistic]!
	
Use won instead
won
	
[BooleanOccurrenceStatistic]!
	
The team's series' wins statistics.
objectives
	
[ObjectiveStatistics!]!
	
The team's accomplished objectives.
firstKill
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the team first kills.
duration
	
DurationStatistic!
	
The aggregated duration of the games.
headshots
	
AggregateIntStatistic!
	
The aggregations of headshots acquired by the team.

GameTeamsStatisticsByGameCs2

The teams statistics for aggregated games.
Field
	
Type
	
Description
money
	
AggregateIntStatistic!
	
The teams' money aggregates.
inventoryValue
	
AggregateIntStatistic!
	
The teams' inventory value aggregates.
netWorth
	
AggregateIntStatistic!
	
The teams' net worth aggregates.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the teams.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the teams.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the teams.
teamkills
	
AggregateIntStatistic!
	
The aggregations of team kills acquired by the teams.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kill assists given by the teams.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kill assists received by the teams.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the teams.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the teams.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the teams.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the teams.
score
	
AggregateIntStatistic!
	
The aggregations of team's score.
objectives
	
[ObjectiveStatistics!]!
	
The teams' accomplished objectives.
unitKills
	
[UnitKillStatistic!]!
	
The aggregation of the unit kills by the teams.
players
	
TeamPlayersStatisticsByGame!
	
The aggregation of team players statistics.
damageDealt
	
AggregateIntStatistic!
	
The aggregation of the damage dealt by the teams.
damageTaken
	
AggregateIntStatistic!
	
The aggregation of the damage taken by the teams.

PlayerGameStatisticsCs2

The player statistics for aggregated games.
Field
	
Type
	
Description
count
	
Int!
	
The number of aggregated games.
wins
	
[BooleanOccurrenceStatistic]!
	
Use won instead
won
	
[BooleanOccurrenceStatistic]!
	
The player's games' wins statistics.
money
	
AggregateIntStatistic!
	
The player's money aggregates.
inventoryValue
	
AggregateIntStatistic!
	
The player's inventory value aggregates.
netWorth
	
AggregateIntStatistic!
	
The player's net worth aggregates.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the player.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the player.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the player.
teamkills
	
AggregateIntStatistic!
	
The aggregations of team kills acquired by the player.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kill assists given by the player.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kill assists received by the player.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the player.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the player.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the player.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the player.
objectives
	
[ObjectiveStatistics!]!
	
The player's accomplished objectives.
firstKill
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the player first kills.
unitKills
	
[UnitKillStatistic!]!
	
The aggregation of the unit kills by the player.
damageDealt
	
AggregateIntStatistic!
	
The aggregation of the damage dealt by the player.
damageTaken
	
AggregateIntStatistic!
	
The aggregation of the damage taken by the teams.

PlayerSegmentStatisticsCs2

The player statistics for aggregated game segments.
Field
	
Type
	
Description
type
	
String!
	
The type of game segment.
count
	
Int!
	
The number of aggregated game segments.
wins
	
[BooleanOccurrenceStatistic]!
	
Use won instead
won
	
[BooleanOccurrenceStatistic]!
	
The player's game segments' wins statistics.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the player.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the player.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the player.
teamkills
	
AggregateIntStatistic!
	
The aggregations of team kills acquired by the player.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kill assists given by the player.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kill assists received by the player.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the player.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the player.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the player.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the player.
objectives
	
[ObjectiveStatistics!]!
	
The player's accomplished objectives.
firstKill
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the player first kills.
damageDealt
	
AggregateIntStatistic!
	
The aggregation of the damage dealt by the player.

TeamGameStatisticsCs2

The team statistics for aggregated games.
Field
	
Type
	
Description
count
	
Int!
	
The number of aggregated games.
money
	
AggregateIntStatistic!
	
The team's money aggregates.
inventoryValue
	
AggregateIntStatistic!
	
The team's inventory value aggregates.
netWorth
	
AggregateIntStatistic!
	
The team's net worth aggregates.
wins
	
[BooleanOccurrenceStatistic]!
	
Use won instead
won
	
[BooleanOccurrenceStatistic]!
	
The team's games' wins statistics.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the team.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the team.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the team.
teamkills
	
AggregateIntStatistic!
	
The aggregations of team kills acquired by the team.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kill assists given by the team.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kill assists received by the team.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the team.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the team.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the team.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the team.
score
	
AggregateIntStatistic!
	
The aggregations of team's score.
objectives
	
[ObjectiveStatistics!]!
	
The team's accomplished objectives.
firstKill
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the team first kills.
unitKills
	
[UnitKillStatistic!]!
	
The aggregation of the unit kills by the player.
players
	
PlayerGameStatisticsByTeam!
	
The player specific aggregations.
duration
	
DurationStatistic!
	
The aggregated duration of the games.
damageDealt
	
AggregateIntStatistic!
	
The aggregation of the damage dealt by the team.
damageTaken
	
AggregateIntStatistic!
	
The aggregation of the damage taken by the teams.

TeamSegmentStatisticsCs2

The team statistics for aggregated game segments.
Field
	
Type
	
Description
type
	
String!
	
The type of game segment.
count
	
Int!
	
The number of aggregated game segments.
wins
	
[BooleanOccurrenceStatistic]!
	
Use won instead
won
	
[BooleanOccurrenceStatistic]!
	
The team's game segments' wins statistics.
kills
	
AggregateIntStatistic!
	
The aggregations of kills acquired by the team.
killAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of enemy kills given by the team.
killAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of enemy kills received by the team.
teamkills
	
AggregateIntStatistic!
	
The aggregations of team kills acquired by the team.
teamkillAssistsGiven
	
AggregateIntStatistic!
	
The aggregations of allied kill assists given by the team.
teamkillAssistsReceived
	
AggregateIntStatistic!
	
The aggregations of allied kill assists received by the team.
selfkills
	
AggregateIntStatistic!
	
The aggregations of self kills committed by the team.
deaths
	
AggregateIntStatistic!
	
The aggregations of deaths committed by the team.
structuresDestroyed
	
AggregateIntStatistic!
	
The aggregations of structures destroyed by the team.
structuresCaptured
	
AggregateIntStatistic!
	
The aggregations of structures captured by the team.
objectives
	
[ObjectiveStatistics!]!
	
The team's accomplished objectives.
firstKill
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the team first kills.
wonFirst
	
[BooleanOccurrenceStatistic!]!
	
The aggregation of the first segment wins achieved by the team.
damageDealt
	
AggregateIntStatistic!
	
The aggregation of the damage dealt by the team.

UNKNOWN
	
Unknown error. This error should only be returned when no other error detail applies. If a client sees an unknown errorDetail, it will be interpreted as UNKNOWN. HTTP Mapping: 500 Internal Server Error
FIELD_NOT_FOUND
	
The requested field is not found in the schema. This differs from
INVALID_CURSOR
	
The provided cursor is not valid. The most common usage for this error is when a client is paginating through a list that uses stateful cursors. In that case, the provided cursor may be expired. HTTP Mapping: 404 Not Found Error Type: NOT_FOUND
UNIMPLEMENTED
	
The operation is not implemented or is not currently supported/enabled. HTTP Mapping: 501 Not Implemented Error Type: BAD_REQUEST
INVALID_ARGUMENT
	
The client specified an invalid argument. Note that this differs from
DEADLINE_EXCEEDED
	
The deadline expired before the operation could complete. For operations that change the state of the system, this error may be returned even if the operation has completed successfully. For example, a successful response from a server could have been delayed long enough for the deadline to expire. HTTP Mapping: 504 Gateway Timeout Error Type: UNAVAILABLE
SERVICE_ERROR
	
Service Error. There is a problem with an upstream service. This may be returned if a gateway receives an unknown error from a service or if a service is unreachable. If a request times out which waiting on a response from a service,
THROTTLED_CPU
	
Request throttled based on server CPU limits HTTP Mapping: 503 Unavailable. Error Type: UNAVAILABLE
THROTTLED_CONCURRENCY
	
Request throttled based on server concurrency limits. HTTP Mapping: 503 Unavailable Error Type: UNAVAILABLE
ENHANCE_YOUR_CALM
	
The server detected that the client is exhibiting a behavior that might be generating excessive load. HTTP Mapping: 420 Enhance Your Calm Error Type: UNAVAILABLE
TOO_MANY_REQUESTS
	
The server detected that the client is exhibiting a behavior that might be generating excessive load. HTTP Mapping: 429 Too Many Requests Error Type: UNAVAILABLE
TCP_FAILURE
	
Request failed due to network errors. HTTP Mapping: 503 Unavailable Error Type: UNAVAILABLE
MISSING_RESOURCE
	
Unable to perform operation because a required resource is missing. Example: Client is attempting to refresh a list, but the specified list is expired. This requires an action by the client to get a new list. If the user is simply trying GET a resource that is not found, use the NOT_FOUND error type. FAILED_PRECONDITION.MISSING_RESOURCE is to be used particularly when the user is performing an operation that requires a particular resource to exist. HTTP Mapping: 400 Bad Request or 500 Internal Server Error Error Type: FAILED_PRECONDITION
ErrorType
Field
	
Description
UNKNOWN
	
Unknown error. For example, this error may be returned when an error code received from another address space belongs to an error space that is not known in this address space. Also errors raised by APIs that do not return enough error information may be converted to this error. If a client sees an unknown errorType, it will be interpreted as UNKNOWN. Unknown errors MUST NOT trigger any special behavior. These MAY be treated by an implementation as being equivalent to INTERNAL. When possible, a more specific error should be provided. HTTP Mapping: 520 Unknown Error
INTERNAL
	
Internal error. An unexpected internal error was encountered. This means that some invariants expected by the underlying system have been broken. This error code is reserved for serious errors. HTTP Mapping: 500 Internal Server Error
NOT_FOUND
	
The requested entity was not found. This could apply to a resource that has never existed (e.g. bad resource id), or a resource that no longer exists (e.g. cache expired.) Note to server developers: if a request is denied for an entire class of users, such as gradual feature rollout or undocumented allowlist,
UNAUTHENTICATED
	
The request does not have valid authentication credentials. This is intended to be returned only for routes that require authentication. HTTP Mapping: 401 Unauthorized
PERMISSION_DENIED
	
The caller does not have permission to execute the specified operation.
BAD_REQUEST
	
Bad Request. There is a problem with the request. Retrying the same request is not likely to succeed. An example would be a query or argument that cannot be deserialized. HTTP Mapping: 400 Bad Request
UNAVAILABLE
	
Currently Unavailable. The service is currently unavailable. This is most likely a transient condition, which can be corrected by retrying with a backoff. HTTP Mapping: 503 Unavailable
FAILED_PRECONDITION
	
The operation was rejected because the system is not in a state required for the operation's execution. For example, the directory to be deleted is non-empty, an rmdir operation is applied to a non-directory, etc. Service implementers can use the following guidelines to decide between 