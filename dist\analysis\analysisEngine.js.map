{"version": 3, "file": "analysisEngine.js", "sourceRoot": "", "sources": ["../../src/analysis/analysisEngine.ts"], "names": [], "mappings": ";;;AAAA,kDAAkD;AAClD,2DAAwD;AACxD,qDAAkD;AAClD,6DAA0D;AAC1D,kDAAiD;AAWjD,MAAa,cAAc;IAIzB,YAAY,MAAc,EAAE,MAAsB;QAChD,IAAI,CAAC,UAAU,GAAG,IAAI,0BAAa,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,aAA4B,EAAE,WAAuB;QACxE,OAAO,CAAC,GAAG,CAAC,sCAAsC,aAAa,KAAK,CAAC,CAAC;QAEtE,0BAA0B;QAC1B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;QAC/D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,+BAA+B;QAC/B,MAAM,UAAU,GAAG,uBAAW,CAAC,qBAAqB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACjF,OAAO,CAAC,GAAG,CAAC,uBAAuB,uBAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,uBAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAE9J,sCAAsC;QACtC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;YAC1D,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,KAAK;YAC/B,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,GAAG;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAChE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,YAAY,eAAe,CAAC,KAAK,CAAC,MAAM,qBAAqB,CAAC,CAAC;QAE3E,qBAAqB;QACrB,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,KAAK,MAAM,IAAI,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;YAEzB,IAAI,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAEnE,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChE,8BAA8B;YAChC,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACrB,IAAI,CAAC,CAAC,cAAc,CAAC,UAAU,KAAK,CAAC,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBAChE,OAAO,CAAC,CAAC,cAAc,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC;YACnE,CAAC;YACD,OAAO,CAAC,CAAC,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,CAAC,MAAM,oCAAoC,CAAC,CAAC;QACzF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,MAAiB,EACjB,UAAgE;QAGhE,+BAA+B;QAC/B,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,CAAC,EAAE,0BAA0B,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,CAAC;YACH,sBAAsB;YACtB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;gBAChE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;aACjE,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,QAAQ,GAAG,qCAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,QAAQ,GAAG,qCAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7E,uCAAuC;YACvC,IAAI,YAAY,GAAyB,EAAE,CAAC;YAC5C,IAAI,YAAY,GAAyB,EAAE,CAAC;YAE5C,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAClD,CAAC,YAAY,EAAE,YAAY,CAAC,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACzD,KAAK,CAAC,IAAI,CAAC,OAAO,EAClB,KAAK,CAAC,IAAI,CAAC,OAAO,EAClB,UAAU,CAAC,iBAAiB,CAC7B,CAAC;YACJ,CAAC;YAED,4BAA4B;YAC5B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CACjD,KAAK,CAAC,IAAI,CAAC,EAAE,EACb,KAAK,CAAC,IAAI,CAAC,EAAE,EACb,UAAU,CAAC,UAAU,CACtB,CAAC;YAEF,MAAM,UAAU,GAAG,uCAAkB,CAAC,iBAAiB,CACrD,KAAK,CAAC,IAAI,CAAC,EAAE,EACb,KAAK,CAAC,IAAI,CAAC,EAAE,EACb,KAAK,CAAC,IAAI,CAAC,IAAI,EACf,KAAK,CAAC,IAAI,CAAC,IAAI,EACf,OAAO,CACR,CAAC;YAEF,wBAAwB;YACxB,MAAM,UAAU,GAAG;gBACjB,YAAY,EAAE,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU;gBAC9D,YAAY,EAAE,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU;aAC/D,CAAC;YAEF,sBAAsB;YACtB,MAAM,WAAW,GAAG;gBAClB,eAAe,EAAE,GAAG,EAAE,4BAA4B;gBAClD,UAAU,EAAE;oBACV,KAAK,EAAE,UAAU,CAAC,UAAU;oBAC5B,KAAK,EAAE,UAAU,CAAC,UAAU;iBAC7B;aACF,CAAC;YAEF,mBAAmB;YACnB,MAAM,QAAQ,GAAG,+BAAc,CAAC,cAAc,CAC5C,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,WAAW,CACZ,CAAC;YAEF,kCAAkC;YAClC,MAAM,cAAc,GAAG,+BAAc,CAAC,6BAA6B,CACjE,KAAK,CAAC,IAAI,CAAC,IAAI,EACf,KAAK,CAAC,IAAI,CAAC,IAAI,EACf,QAAQ,EACR,QAAQ,EACR,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,gDAAgD;YAC9E,QAAQ,CACT,CAAC;YAEF,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,4CAA4C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACtF,OAAO,IAAI,CAAC;YACd,CAAC;YAED,0BAA0B;YAC1B,MAAM,QAAQ,GAAkB;gBAC9B,OAAO,EAAE,MAAM,CAAC,EAAE;gBAClB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;gBACtB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;gBACtB,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;gBAClC,MAAM,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE;gBAC5B,aAAa,EAAE,MAAM,CAAC,WAAW;gBACjC,WAAW,EAAE;oBACX,KAAK,EAAE,IAAI,EAAE,YAAY;oBACzB,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,QAAQ;iBACnB;gBACD,cAAc;gBACd,mBAAmB,EAAE;oBACnB,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,QAAQ;iBAChB;gBACD,mBAAmB,EAAE;oBACnB,KAAK,EAAE,YAAY;oBACnB,KAAK,EAAE,YAAY;iBACpB;gBACD,UAAU;gBACV,cAAc,EAAE;oBACd,YAAY,EAAE,IAAI;oBAClB,gBAAgB,EAAE,GAAG;oBACrB,aAAa,EAAE,MAAM;oBACrB,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;iBAC1C;gBACD,oBAAoB,EAAE;oBACpB,kBAAkB,EAAE;wBAClB,KAAK,EAAE,uBAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,aAAa;wBACxE,KAAK,EAAE,uBAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC;qBAC1D;oBACD,iBAAiB,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,SAAS;oBACtD,iBAAiB,EAAE,CAAC,EAAE,aAAa;oBACnC,qBAAqB,EAAE,uBAAW,CAAC,yBAAyB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU;iBACvG;aACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,YAAmB,EACnB,YAAmB,EACnB,SAAoB;QAGpB,MAAM,kBAAkB,GAAG,KAAK,EAAE,OAAc,EAAiC,EAAE;YACjF,MAAM,aAAa,GAAyB,EAAE,CAAC;YAE/C,+CAA+C;YAC/C,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEvC,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;gBAChC,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oBAC/E,MAAM,GAAG,GAAG,qCAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;oBAErE,IAAI,GAAG,EAAE,CAAC;wBACR,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC;wBACzB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC1B,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;oBACpE,8BAA8B;gBAChC,CAAC;YACH,CAAC;YAED,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC,CAAC;QAEF,MAAM,CAAC,YAAY,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACrD,kBAAkB,CAAC,YAAY,CAAC;YAChC,kBAAkB,CAAC,YAAY,CAAC;SACjC,CAAC,CAAC;QAEH,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,MAAiB;QACrC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC;QAClC,CAAC;QAED,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAkC;QAC7C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,GAAG,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;CACF;AA3SD,wCA2SC"}