{"version": 3, "file": "statisticalEngine.js", "sourceRoot": "", "sources": ["../../src/analysis/statisticalEngine.ts"], "names": [], "mappings": ";;;AASA,kDAAiD;AAEjD,MAAa,iBAAiB;IAE5B;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,SAAuB,EAAE,QAAgB;QAC3D,4CAA4C;QAC5C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,UAAU,SAAS,EAAE,UAAU,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3G,CAAC;QAED,yBAAyB;QACzB,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,UAAU;YACvD,GAAG,EAAE,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM;YACvC,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,YAAY,EAAE,SAAS,CAAC,kBAAkB,GAAG,GAAG;YAChD,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC;YAC3D,aAAa,EAAE,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,WAAW;SAC5D,CAAC;QAEF,iCAAiC;QACjC,MAAM,iBAAiB,GAAG;YACxB,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,cAAc;YACxC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;YACnC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;YACnC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC;YAC5C,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,cAAc;YAC/D,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;YACjD,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;SAClD,CAAC;QAEF,+BAA+B;QAC/B,MAAM,GAAG,GAAG,CACV,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;YAClC,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,CAAC;YAC9B,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,CAAC;YAC9B,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC;YACjC,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC;YACvC,CAAC,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC;YACxC,CAAC,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC,CACzC,GAAG,GAAG,CAAC;QAER,6CAA6C;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAExE,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,qBAAqB;YACvD,UAAU;YACV,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,UAAU,EAAE,uBAAW,CAAC,aAAa,EAAE;YACvC,OAAO;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,WAA2B,EAAE,UAAkB;QACjE,4CAA4C;QAC5C,IAAI,CAAC,WAAW;YACZ,WAAW,CAAC,UAAU,GAAG,CAAC;YAC1B,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,CAAC,wBAAwB;QACvC,CAAC;QAED,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM;YAC3C,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,YAAY,EAAE,WAAW,CAAC,kBAAkB,GAAG,GAAG;YAClD,UAAU,EAAE,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;SACpG,CAAC;QAEF,yBAAyB;QACzB,MAAM,GAAG,GAAG,CACV,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACvC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACvC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;YAC7C,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;YAC1C,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CACzC,GAAG,GAAG,CAAC;QAER,OAAO;YACL,QAAQ,EAAE,EAAE,EAAE,wBAAwB;YACtC,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE;YACjC,WAAW,EAAE,WAAW,CAAC,UAAU;YACnC,UAAU,EAAE,WAAW,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;YAC5D,OAAO;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,QAAqB;QACtD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QACjH,MAAM,mBAAmB,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAE9H,OAAO,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CAAC,GAAW;QACrC,qEAAqE;QACrE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CAAC,GAAW;QACrC,qDAAqD;QACrD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,MAAc;QAC3C,2DAA2D;QAC3D,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,UAAkB,EAAE,aAAsB;QAC3E,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,wBAAwB,CAAC,UAAsB;QACpD,OAAO,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,aAAmC;QAC7D,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEzC,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;QAC3G,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEhF,+CAA+C;QAC/C,MAAM,YAAY,GAAG,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAE/E,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,OAAiB;QAChD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEnC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QACzE,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjE,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,UAAkB,EAAE,gBAAwB;QACnE,IAAI,gBAAgB,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAErC,MAAM,OAAO,GAAG,UAAU,GAAG,gBAAgB,CAAC;QAE9C,mCAAmC;QACnC,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,2CAA2C;YAC3C,OAAO,OAAO,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,+BAA+B,CACpC,SAAuB,EACvB,WAAmB,CAAC;QAEpB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,6BAA6B,EAAE,CAAC;QACnE,CAAC;QAED,IAAI,SAAS,CAAC,UAAU,GAAG,QAAQ,EAAE,CAAC;YACpC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,uBAAuB,SAAS,CAAC,UAAU,MAAM,QAAQ,EAAE;aACpE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gCAAgC,EAAE,CAAC;QACtE,CAAC;QAED,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,yBAAyB,EAAE,CAAC;QAC/D,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,cAAsB,EACtB,IAAY,EACZ,UAAkB;QAElB,yCAAyC;QACzC,MAAM,mBAAmB,GAAG,cAAc,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QAEhE,yEAAyE;QACzE,MAAM,EAAE,GAAG,CAAC,mBAAmB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC;QAE1E,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,sCAAsC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,cAAsB,EACtB,IAAY,EACZ,UAAkB,EAClB,WAAmB,IAAI,CAAC,eAAe;;QAEvC,MAAM,mBAAmB,GAAG,cAAc,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;QAEhE,0EAA0E;QAC1E,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;QACnB,MAAM,CAAC,GAAG,mBAAmB,CAAC;QAC9B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEhB,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAEtC,oDAAoD;QACpD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxD,CAAC;CACF;AAjQD,8CAiQC"}