"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticalEngine = void 0;
const dateUtils_1 = require("../utils/dateUtils");
class StatisticalEngine {
    static calculateTDS(teamStats, teamName) {
        if (!teamStats || teamStats.gamesCount < 5) {
            throw new Error(`Insufficient real data for team ${teamName}: only ${teamStats?.gamesCount || 0} games`);
        }
        const metrics = {
            winRate: teamStats.gamesWonCount / teamStats.gamesCount,
            kdr: teamStats.kills / teamStats.deaths,
            adr: teamStats.adr,
            rating: teamStats.rating,
            headshotRate: teamStats.headshotPercentage / 100,
            clutchSuccess: this.calculateClutchRate(teamStats.clutches),
            firstKillRate: teamStats.firstKills / teamStats.roundsCount
        };
        const normalizedMetrics = {
            winRate: metrics.winRate,
            kdr: this.normalizeKDR(metrics.kdr),
            adr: this.normalizeADR(metrics.adr),
            rating: this.normalizeRating(metrics.rating),
            headshotRate: Math.min(metrics.headshotRate, 1),
            clutchSuccess: Math.min(metrics.clutchSuccess, 1),
            firstKillRate: Math.min(metrics.firstKillRate, 1)
        };
        const tds = ((normalizedMetrics.winRate * 0.25) +
            (normalizedMetrics.kdr * 0.20) +
            (normalizedMetrics.adr * 0.15) +
            (normalizedMetrics.rating * 0.15) +
            (normalizedMetrics.headshotRate * 0.10) +
            (normalizedMetrics.clutchSuccess * 0.10) +
            (normalizedMetrics.firstKillRate * 0.05)) * 100;
        const confidence = this.calculateConfidence(teamStats.gamesCount, true);
        return {
            score: Math.round(tds * 10) / 10,
            confidence,
            dataPoints: teamStats.gamesCount,
            lastUpdate: dateUtils_1.DateManager.getCurrentUTC(),
            metrics
        };
    }
    static calculatePIR(playerStats, playerName) {
        if (!playerStats ||
            playerStats.gamesCount < 5 ||
            !playerStats.rating) {
            return null;
        }
        const metrics = {
            kdr: playerStats.kills / playerStats.deaths,
            adr: playerStats.adr,
            rating: playerStats.rating,
            headshotRate: playerStats.headshotPercentage / 100,
            clutchRate: playerStats.clutchesTotal > 0 ? playerStats.clutchesWon / playerStats.clutchesTotal : 0
        };
        const pir = ((this.normalizeKDR(metrics.kdr) * 0.25) +
            (this.normalizeADR(metrics.adr) * 0.20) +
            (this.normalizeRating(metrics.rating) * 0.25) +
            (Math.min(metrics.headshotRate, 1) * 0.15) +
            (Math.min(metrics.clutchRate, 1) * 0.15)) * 100;
        return {
            playerId: '',
            name: playerName,
            rating: Math.round(pir * 10) / 10,
            gamesPlayed: playerStats.gamesCount,
            confidence: playerStats.gamesCount >= 10 ? 'HIGH' : 'MEDIUM',
            metrics
        };
    }
    static calculateClutchRate(clutches) {
        const totalClutchesWon = clutches.won1v1 + clutches.won1v2 + clutches.won1v3 + clutches.won1v4 + clutches.won1v5;
        const totalClutchesPlayed = clutches.total1v1 + clutches.total1v2 + clutches.total1v3 + clutches.total1v4 + clutches.total1v5;
        return totalClutchesPlayed > 0 ? totalClutchesWon / totalClutchesPlayed : 0;
    }
    static normalizeKDR(kdr) {
        return Math.min(kdr / 1.5, 1);
    }
    static normalizeADR(adr) {
        return Math.min(adr / 90, 1);
    }
    static normalizeRating(rating) {
        return Math.min(rating / 1.4, 1);
    }
    static calculateConfidence(gamesCount, dataFreshness) {
        if (!dataFreshness)
            return 'LOW';
        if (gamesCount >= 15)
            return 'HIGH';
        if (gamesCount >= 10)
            return 'MEDIUM';
        return 'LOW';
    }
    static calculateMultiKillImpact(multiKills) {
        return (multiKills.k2 * 1) + (multiKills.k3 * 2) + (multiKills.k4 * 4) + (multiKills.k5 * 8);
    }
    static calculateTeamSynergy(playerRatings) {
        if (playerRatings.length === 0)
            return 0;
        const averageRating = playerRatings.reduce((sum, player) => sum + player.rating, 0) / playerRatings.length;
        const ratingVariance = this.calculateVariance(playerRatings.map(p => p.rating));
        const synergyScore = averageRating * (1 - Math.min(ratingVariance / 100, 0.5));
        return Math.max(synergyScore, 0);
    }
    static calculateVariance(numbers) {
        if (numbers.length === 0)
            return 0;
        const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
        const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
    }
    static calculateMomentum(recentWins, totalRecentGames) {
        if (totalRecentGames === 0)
            return 0;
        const winRate = recentWins / totalRecentGames;
        if (totalRecentGames >= 5) {
            return winRate;
        }
        else {
            return winRate * (totalRecentGames / 5);
        }
    }
    static validateStatisticalSignificance(teamStats, minGames = 5) {
        if (!teamStats) {
            return { isValid: false, reason: 'No team statistics provided' };
        }
        if (teamStats.gamesCount < minGames) {
            return {
                isValid: false,
                reason: `Insufficient games: ${teamStats.gamesCount} < ${minGames}`
            };
        }
        if (!teamStats.rating || teamStats.rating === 0) {
            return { isValid: false, reason: 'Missing or invalid rating data' };
        }
        if (teamStats.kills === 0 || teamStats.deaths === 0) {
            return { isValid: false, reason: 'Missing kill/death data' };
        }
        return { isValid: true };
    }
    static calculateExpectedValue(winProbability, odds, confidence) {
        const adjustedProbability = winProbability * (confidence / 100);
        const ev = (adjustedProbability * (odds - 1)) - (1 - adjustedProbability);
        return Math.round(ev * 1000) / 10;
    }
    static calculateKellyStake(winProbability, odds, confidence, maxStake = 0.05) {
        const adjustedProbability = winProbability * (confidence / 100);
        const b = odds - 1;
        const p = adjustedProbability;
        const q = 1 - p;
        const kellyFraction = (b * p - q) / b;
        return Math.max(0, Math.min(kellyFraction, maxStake));
    }
}
exports.StatisticalEngine = StatisticalEngine;
//# sourceMappingURL=statisticalEngine.js.map