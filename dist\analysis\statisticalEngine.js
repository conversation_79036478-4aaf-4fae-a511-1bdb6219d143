"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticalEngine = void 0;
const dateUtils_1 = require("../utils/dateUtils");
class StatisticalEngine {
    /**
     * Calculate Team Dominance Score (TDS) based on real GRID API data
     */
    static calculateTDS(teamStats, teamName) {
        // Validate data exists and is not simulated
        if (!teamStats || teamStats.gamesCount < 5) {
            throw new Error(`Insufficient real data for team ${teamName}: only ${teamStats?.gamesCount || 0} games`);
        }
        // Calculate core metrics
        const metrics = {
            winRate: teamStats.gamesWonCount / teamStats.gamesCount,
            kdr: teamStats.kills / teamStats.deaths,
            adr: teamStats.adr,
            rating: teamStats.rating,
            headshotRate: teamStats.headshotPercentage / 100,
            clutchSuccess: this.calculateClutchRate(teamStats.clutches),
            firstKillRate: teamStats.firstKills / teamStats.roundsCount
        };
        // Normalize metrics to 0-1 scale
        const normalizedMetrics = {
            winRate: metrics.winRate, // Already 0-1
            kdr: this.normalizeKDR(metrics.kdr),
            adr: this.normalizeADR(metrics.adr),
            rating: this.normalizeRating(metrics.rating),
            headshotRate: Math.min(metrics.headshotRate, 1), // Cap at 100%
            clutchSuccess: Math.min(metrics.clutchSuccess, 1),
            firstKillRate: Math.min(metrics.firstKillRate, 1)
        };
        // Calculate weighted TDS score
        const tds = ((normalizedMetrics.winRate * 0.25) +
            (normalizedMetrics.kdr * 0.20) +
            (normalizedMetrics.adr * 0.15) +
            (normalizedMetrics.rating * 0.15) +
            (normalizedMetrics.headshotRate * 0.10) +
            (normalizedMetrics.clutchSuccess * 0.10) +
            (normalizedMetrics.firstKillRate * 0.05)) * 100;
        // Calculate confidence based on data quality
        const confidence = this.calculateConfidence(teamStats.gamesCount, true);
        return {
            score: Math.round(tds * 10) / 10, // Round to 1 decimal
            confidence,
            dataPoints: teamStats.gamesCount,
            lastUpdate: dateUtils_1.DateManager.getCurrentUTC(),
            metrics
        };
    }
    /**
     * Calculate Player Impact Rating (PIR) based on verified real data
     */
    static calculatePIR(playerStats, playerName) {
        // Strict validation - no mock data accepted
        if (!playerStats ||
            playerStats.gamesCount < 5 ||
            !playerStats.rating) {
            return null; // Exclude from analysis
        }
        const metrics = {
            kdr: playerStats.kills / playerStats.deaths,
            adr: playerStats.adr,
            rating: playerStats.rating,
            headshotRate: playerStats.headshotPercentage / 100,
            clutchRate: playerStats.clutchesTotal > 0 ? playerStats.clutchesWon / playerStats.clutchesTotal : 0
        };
        // Calculate weighted PIR
        const pir = ((this.normalizeKDR(metrics.kdr) * 0.25) +
            (this.normalizeADR(metrics.adr) * 0.20) +
            (this.normalizeRating(metrics.rating) * 0.25) +
            (Math.min(metrics.headshotRate, 1) * 0.15) +
            (Math.min(metrics.clutchRate, 1) * 0.15)) * 100;
        return {
            playerId: '', // Will be set by caller
            name: playerName,
            rating: Math.round(pir * 10) / 10,
            gamesPlayed: playerStats.gamesCount,
            confidence: playerStats.gamesCount >= 10 ? 'HIGH' : 'MEDIUM',
            metrics
        };
    }
    /**
     * Calculate clutch success rate from clutch statistics
     */
    static calculateClutchRate(clutches) {
        const totalClutchesWon = clutches.won1v1 + clutches.won1v2 + clutches.won1v3 + clutches.won1v4 + clutches.won1v5;
        const totalClutchesPlayed = clutches.total1v1 + clutches.total1v2 + clutches.total1v3 + clutches.total1v4 + clutches.total1v5;
        return totalClutchesPlayed > 0 ? totalClutchesWon / totalClutchesPlayed : 0;
    }
    /**
     * Normalize K/D ratio to 0-1 scale
     */
    static normalizeKDR(kdr) {
        // Excellent KDR is around 1.3+, normalize with sigmoid-like function
        return Math.min(kdr / 1.5, 1);
    }
    /**
     * Normalize ADR to 0-1 scale
     */
    static normalizeADR(adr) {
        // Excellent ADR is around 80+, normalize accordingly
        return Math.min(adr / 90, 1);
    }
    /**
     * Normalize rating to 0-1 scale
     */
    static normalizeRating(rating) {
        // HLTV rating 2.0 scale: 1.0 is average, 1.3+ is excellent
        return Math.min(rating / 1.4, 1);
    }
    /**
     * Calculate confidence level based on data quality
     */
    static calculateConfidence(gamesCount, dataFreshness) {
        if (!dataFreshness)
            return 'LOW';
        if (gamesCount >= 15)
            return 'HIGH';
        if (gamesCount >= 10)
            return 'MEDIUM';
        return 'LOW';
    }
    /**
     * Calculate multi-kill impact score
     */
    static calculateMultiKillImpact(multiKills) {
        return (multiKills.k2 * 1) + (multiKills.k3 * 2) + (multiKills.k4 * 4) + (multiKills.k5 * 8);
    }
    /**
     * Calculate team synergy score based on individual player ratings
     */
    static calculateTeamSynergy(playerRatings) {
        if (playerRatings.length === 0)
            return 0;
        const averageRating = playerRatings.reduce((sum, player) => sum + player.rating, 0) / playerRatings.length;
        const ratingVariance = this.calculateVariance(playerRatings.map(p => p.rating));
        // Lower variance indicates better team balance
        const synergyScore = averageRating * (1 - Math.min(ratingVariance / 100, 0.5));
        return Math.max(synergyScore, 0);
    }
    /**
     * Calculate variance of an array of numbers
     */
    static calculateVariance(numbers) {
        if (numbers.length === 0)
            return 0;
        const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
        const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
    }
    /**
     * Calculate momentum score based on recent performance trend
     */
    static calculateMomentum(recentWins, totalRecentGames) {
        if (totalRecentGames === 0)
            return 0;
        const winRate = recentWins / totalRecentGames;
        // Weight recent games more heavily
        if (totalRecentGames >= 5) {
            return winRate;
        }
        else {
            // Reduce confidence for small sample sizes
            return winRate * (totalRecentGames / 5);
        }
    }
    /**
     * Validate statistical significance of data
     */
    static validateStatisticalSignificance(teamStats, minGames = 5) {
        if (!teamStats) {
            return { isValid: false, reason: 'No team statistics provided' };
        }
        if (teamStats.gamesCount < minGames) {
            return {
                isValid: false,
                reason: `Insufficient games: ${teamStats.gamesCount} < ${minGames}`
            };
        }
        if (!teamStats.rating || teamStats.rating === 0) {
            return { isValid: false, reason: 'Missing or invalid rating data' };
        }
        if (teamStats.kills === 0 || teamStats.deaths === 0) {
            return { isValid: false, reason: 'Missing kill/death data' };
        }
        return { isValid: true };
    }
    /**
     * Calculate expected value for betting recommendation
     */
    static calculateExpectedValue(winProbability, odds, confidence) {
        // Adjust probability based on confidence
        const adjustedProbability = winProbability * (confidence / 100);
        // Expected value formula: (probability * (odds - 1)) - (1 - probability)
        const ev = (adjustedProbability * (odds - 1)) - (1 - adjustedProbability);
        return Math.round(ev * 1000) / 10; // Return as percentage with 1 decimal
    }
    /**
     * Calculate Kelly criterion stake size
     */
    static calculateKellyStake(winProbability, odds, confidence, maxStake = 0.05 // 5% max stake
    ) {
        const adjustedProbability = winProbability * (confidence / 100);
        // Kelly formula: (bp - q) / b, where b = odds-1, p = probability, q = 1-p
        const b = odds - 1;
        const p = adjustedProbability;
        const q = 1 - p;
        const kellyFraction = (b * p - q) / b;
        // Apply maximum stake limit and ensure non-negative
        return Math.max(0, Math.min(kellyFraction, maxStake));
    }
}
exports.StatisticalEngine = StatisticalEngine;
//# sourceMappingURL=statisticalEngine.js.map