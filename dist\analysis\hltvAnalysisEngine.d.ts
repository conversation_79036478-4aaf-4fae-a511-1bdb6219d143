import { MatchAnalysis, AnalysisConfig, DateSelection, DateRange } from '../types';
export declare class HLTVAnalysisEngine {
    private hltvClient;
    private config;
    constructor(config: AnalysisConfig);
    analyzeMatches(dateSelection: DateSelection, customDates?: DateRange): Promise<MatchAnalysis[]>;
    private analyzeSingleMatch;
    private getKnownPlayersForTeam;
    private getPlayerAnalysis;
    private calculateTDSFromHLTV;
    updateConfig(newConfig: Partial<AnalysisConfig>): void;
    getConfig(): AnalysisConfig;
}
//# sourceMappingURL=hltvAnalysisEngine.d.ts.map