"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DateManager = void 0;
const moment_1 = __importDefault(require("moment"));
class DateManager {
    /**
     * Get date ranges for different analysis periods based on user selection
     */
    static getAnalysisDateRanges(selectedOption, customDates) {
        const matchDate = this.getMatchDateRange(selectedOption, customDates);
        const matchStartMoment = (0, moment_1.default)(matchDate.start).utc();
        return {
            matchDate,
            // Team form analysis (last 30 days from match date)
            teamForm: {
                start: matchStartMoment.clone().subtract(30, 'days').toISOString(),
                end: matchStartMoment.clone().subtract(1, 'day').toISOString()
            },
            // Head-to-head history (last 12 months)
            headToHead: {
                start: matchStartMoment.clone().subtract(12, 'months').toISOString(),
                end: matchStartMoment.clone().subtract(1, 'day').toISOString()
            },
            // Player consistency (last 60 days)
            playerConsistency: {
                start: matchStartMoment.clone().subtract(60, 'days').toISOString(),
                end: matchStartMoment.clone().subtract(1, 'day').toISOString()
            }
        };
    }
    /**
     * Get match date range based on selection option
     */
    static getMatchDateRange(selectedOption, customDates) {
        const now = (0, moment_1.default)().utc();
        switch (selectedOption) {
            case 'TODAY':
                return {
                    start: now.clone().startOf('day').toISOString(),
                    end: now.clone().endOf('day').toISOString()
                };
            case 'TOMORROW':
                return {
                    start: now.clone().add(1, 'day').startOf('day').toISOString(),
                    end: now.clone().add(1, 'day').endOf('day').toISOString()
                };
            case 'CUSTOM':
                if (!customDates) {
                    throw new Error('Custom dates must be provided when using CUSTOM date selection');
                }
                return {
                    start: (0, moment_1.default)(customDates.start).utc().startOf('day').toISOString(),
                    end: (0, moment_1.default)(customDates.end).utc().endOf('day').toISOString()
                };
            default:
                throw new Error(`Invalid date selection: ${selectedOption}`);
        }
    }
    /**
     * Get all available date options with current UTC timestamps
     */
    static getDateOptions() {
        const now = (0, moment_1.default)().utc();
        return {
            TODAY: {
                start: now.clone().startOf('day').toISOString(),
                end: now.clone().endOf('day').toISOString()
            },
            TOMORROW: {
                start: now.clone().add(1, 'day').startOf('day').toISOString(),
                end: now.clone().add(1, 'day').endOf('day').toISOString()
            },
            CUSTOM: {
                start: '[USER_INPUT_START_DATE]T00:00:00.000Z',
                end: '[USER_INPUT_END_DATE]T23:59:59.999Z'
            }
        };
    }
    /**
     * Validate date range is valid
     */
    static validateDateRange(dateRange) {
        const start = (0, moment_1.default)(dateRange.start);
        const end = (0, moment_1.default)(dateRange.end);
        if (!start.isValid() || !end.isValid()) {
            return false;
        }
        if (start.isAfter(end)) {
            return false;
        }
        return true;
    }
    /**
     * Check if data is fresh (within specified hours)
     */
    static isDataFresh(lastUpdate, maxAgeHours = 24) {
        const updateTime = (0, moment_1.default)(lastUpdate);
        const now = (0, moment_1.default)().utc();
        const hoursDiff = now.diff(updateTime, 'hours');
        return hoursDiff <= maxAgeHours;
    }
    /**
     * Calculate days since last match
     */
    static daysSinceLastMatch(lastMatchDate) {
        const lastMatch = (0, moment_1.default)(lastMatchDate);
        const now = (0, moment_1.default)().utc();
        return now.diff(lastMatch, 'days');
    }
    /**
     * Format date for display
     */
    static formatDisplayDate(date) {
        return (0, moment_1.default)(date).utc().format('YYYY-MM-DD HH:mm UTC');
    }
    /**
     * Get current UTC timestamp
     */
    static getCurrentUTC() {
        return (0, moment_1.default)().utc().toISOString();
    }
    /**
     * Parse custom date input and validate
     */
    static parseCustomDate(dateInput) {
        const parsed = (0, moment_1.default)(dateInput, ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm', moment_1.default.ISO_8601], true);
        if (!parsed.isValid()) {
            throw new Error(`Invalid date format: ${dateInput}. Use YYYY-MM-DD format.`);
        }
        return parsed.utc().toISOString();
    }
    /**
     * Get time until match starts
     */
    static getTimeUntilMatch(scheduledAt) {
        const matchTime = (0, moment_1.default)(scheduledAt);
        const now = (0, moment_1.default)().utc();
        const diff = matchTime.diff(now);
        if (diff < 0) {
            return {
                hours: 0,
                minutes: 0,
                isLive: false,
                isPast: true
            };
        }
        const duration = moment_1.default.duration(diff);
        const hours = Math.floor(duration.asHours());
        const minutes = duration.minutes();
        return {
            hours,
            minutes,
            isLive: hours === 0 && minutes <= 15, // Consider live if within 15 minutes
            isPast: false
        };
    }
    /**
     * Check if match is in peak performance window
     */
    static isInPeakPerformanceWindow(scheduledAt) {
        const matchTime = (0, moment_1.default)(scheduledAt);
        const hour = matchTime.hour();
        // Peak performance typically between 14:00-22:00 UTC
        return hour >= 14 && hour <= 22;
    }
    /**
     * Calculate schedule intensity (matches in last 7 days)
     */
    static calculateScheduleIntensity(recentMatches) {
        const now = (0, moment_1.default)().utc();
        const sevenDaysAgo = now.clone().subtract(7, 'days');
        const recentMatchCount = recentMatches.filter(matchDate => {
            const match = (0, moment_1.default)(matchDate);
            return match.isAfter(sevenDaysAgo) && match.isBefore(now);
        }).length;
        return recentMatchCount;
    }
}
exports.DateManager = DateManager;
//# sourceMappingURL=dateUtils.js.map