#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const analysisEngine_1 = require("./analysis/analysisEngine");
const predictionFormatter_1 = require("./output/predictionFormatter");
const dateUtils_1 = require("./utils/dateUtils");
const program = new commander_1.Command();
// GRID API Key from prompt
const GRID_API_KEY = 'cDXLTwMg735stCkxIaaAEdlkYlkKcu88dwjshjYn';
// Default configuration
const DEFAULT_CONFIG = {
    dateSelection: 'TODAY',
    minGamesThreshold: 5,
    confidenceThreshold: 65,
    maxRiskTier: 'HIGH',
    includePlayerAnalysis: true,
    includeMapAnalysis: false
};
program
    .name('cs2-betting-analysis')
    .description('CS2 Betting Analysis Framework with GRID API Integration')
    .version('1.0.0');
program
    .command('analyze')
    .description('Analyze CS2 matches for betting opportunities')
    .option('-d, --date <type>', 'Date selection: TODAY, TOMORROW, or CUSTOM', 'TODAY')
    .option('--start <date>', 'Custom start date (YYYY-MM-DD)')
    .option('--end <date>', 'Custom end date (YYYY-MM-DD)')
    .option('--min-games <number>', 'Minimum games threshold', '5')
    .option('--confidence <number>', 'Minimum confidence threshold', '65')
    .option('--max-risk <tier>', 'Maximum risk tier: LOW, MEDIUM, HIGH', 'HIGH')
    .option('--no-players', 'Disable player analysis')
    .option('--include-maps', 'Include map-specific analysis')
    .option('--summary-only', 'Show only top matches summary')
    .action(async (options) => {
    try {
        console.log(chalk_1.default.blue.bold('🎯 CS2 Betting Analysis Framework'));
        console.log(chalk_1.default.blue('📊 Enhanced Prediction with GRID API Integration'));
        console.log(chalk_1.default.yellow('⚠️  CRITICAL: Using only real, live data - NO MOCK DATA'));
        console.log('');
        // Validate and parse options
        const config = await parseOptions(options);
        // Create analysis engine
        const engine = new analysisEngine_1.AnalysisEngine(GRID_API_KEY, config);
        // Validate configuration
        const validation = engine.validateConfig();
        if (!validation.isValid) {
            console.error(chalk_1.default.red('❌ Configuration errors:'));
            validation.errors.forEach(error => console.error(chalk_1.default.red(`   - ${error}`)));
            process.exit(1);
        }
        // Determine date selection and custom dates
        let dateSelection = config.dateSelection;
        let customDates;
        if (dateSelection === 'CUSTOM') {
            if (!options.start || !options.end) {
                console.error(chalk_1.default.red('❌ Custom date range requires --start and --end parameters'));
                process.exit(1);
            }
            customDates = {
                start: dateUtils_1.DateManager.parseCustomDate(options.start),
                end: dateUtils_1.DateManager.parseCustomDate(options.end)
            };
            if (!dateUtils_1.DateManager.validateDateRange(customDates)) {
                console.error(chalk_1.default.red('❌ Invalid date range'));
                process.exit(1);
            }
        }
        // Run analysis
        console.log(chalk_1.default.green('🚀 Starting analysis...'));
        const startTime = Date.now();
        const analyses = await engine.analyzeMatches(dateSelection, customDates);
        const duration = Date.now() - startTime;
        console.log(chalk_1.default.green(`✅ Analysis completed in ${duration}ms`));
        console.log('');
        // Display results
        if (analyses.length === 0) {
            console.log(chalk_1.default.yellow('⚠️ No betting opportunities found for the specified criteria'));
            return;
        }
        if (options.summaryOnly) {
            // Show summary only
            const summary = predictionFormatter_1.PredictionFormatter.formatTopMatchesSummary(analyses, dateSelection);
            console.log(summary);
        }
        else {
            // Show detailed analysis for each match
            analyses.forEach((analysis, index) => {
                if (index > 0)
                    console.log('\n' + '='.repeat(80) + '\n');
                const formatted = predictionFormatter_1.PredictionFormatter.formatMatchAnalysis(analysis, dateSelection);
                console.log(formatted);
            });
            // Show summary at the end
            console.log('\n' + '='.repeat(80));
            console.log(chalk_1.default.blue.bold('📊 SUMMARY'));
            console.log('='.repeat(80) + '\n');
            const summary = predictionFormatter_1.PredictionFormatter.formatTopMatchesSummary(analyses, dateSelection);
            console.log(summary);
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ Analysis failed:'), error);
        process.exit(1);
    }
});
program
    .command('test-api')
    .description('Test GRID API connection')
    .action(async () => {
    try {
        console.log(chalk_1.default.blue('🔍 Testing GRID API connection...'));
        const engine = new analysisEngine_1.AnalysisEngine(GRID_API_KEY, DEFAULT_CONFIG);
        const isConnected = await engine['gridClient'].validateConnection();
        if (isConnected) {
            console.log(chalk_1.default.green('✅ GRID API connection successful'));
            // Test rate limiting
            const health = engine['gridClient'].getAPIHealth();
            console.log(chalk_1.default.blue(`📊 API Health: ${health.rateLimit} requests used`));
        }
        else {
            console.log(chalk_1.default.red('❌ GRID API connection failed'));
            process.exit(1);
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ API test failed:'), error);
        process.exit(1);
    }
});
program
    .command('dates')
    .description('Show available date options')
    .action(() => {
    console.log(chalk_1.default.blue.bold('📅 Available Date Options:'));
    console.log('');
    const options = dateUtils_1.DateManager.getDateOptions();
    console.log(chalk_1.default.green('TODAY:'));
    console.log(`  Start: ${dateUtils_1.DateManager.formatDisplayDate(options.TODAY.start)}`);
    console.log(`  End:   ${dateUtils_1.DateManager.formatDisplayDate(options.TODAY.end)}`);
    console.log('');
    console.log(chalk_1.default.green('TOMORROW:'));
    console.log(`  Start: ${dateUtils_1.DateManager.formatDisplayDate(options.TOMORROW.start)}`);
    console.log(`  End:   ${dateUtils_1.DateManager.formatDisplayDate(options.TOMORROW.end)}`);
    console.log('');
    console.log(chalk_1.default.green('CUSTOM:'));
    console.log('  Use --start and --end parameters with YYYY-MM-DD format');
    console.log('  Example: --date CUSTOM --start 2024-01-15 --end 2024-01-16');
});
async function parseOptions(options) {
    const config = { ...DEFAULT_CONFIG };
    // Parse date selection
    const dateUpper = options.date.toUpperCase();
    if (!['TODAY', 'TOMORROW', 'CUSTOM'].includes(dateUpper)) {
        throw new Error(`Invalid date selection: ${options.date}`);
    }
    config.dateSelection = dateUpper;
    // Parse numeric options
    config.minGamesThreshold = parseInt(options.minGames);
    config.confidenceThreshold = parseInt(options.confidence);
    // Parse risk tier
    const riskUpper = options.maxRisk.toUpperCase();
    if (!['LOW', 'MEDIUM', 'HIGH'].includes(riskUpper)) {
        throw new Error(`Invalid risk tier: ${options.maxRisk}`);
    }
    config.maxRiskTier = riskUpper;
    // Parse boolean options
    config.includePlayerAnalysis = !options.noPlayers;
    config.includeMapAnalysis = options.includeMaps;
    return config;
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk_1.default.red('❌ Unhandled Rejection at:'), promise, chalk_1.default.red('reason:'), reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(chalk_1.default.red('❌ Uncaught Exception:'), error);
    process.exit(1);
});
program.parse();
//# sourceMappingURL=cli.js.map